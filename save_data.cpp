/*保存数据模块*/
#include <stdio.h>
#include <dirent.h>
#include <fcntl.h>
#include <time.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <queue>
#include <mutex>
#include "DS_Data.h"
#include "TaxData.h"
#include "gps.h"
using namespace std;

queue<RecordFileData> RecordDatQueue;
mutex mtxRecordDatQueue;


extern GPS_INFO GPSData;
extern StaticVar Global_Var;

long what_time_is_it_sec()
{
    long res;
    struct timeval time;
    if (gettimeofday(&time,NULL)){
        return 0;
    }
    res = time.tv_sec;
    return res;
}

void GetInfoFromGlobal(RecordFileData * bw)
{
    bw->BDStat       =  Global_Var.BDS_Stat;
    bw->EquipStatus  =  Global_Var.EquipStatus;
    bw->Net_Stat     =  Global_Var.Net_Stat;
    bw->TrainState   =  Global_Var.TrainState;
    bw->TaxStat      =  Global_Var.TAX_Stat;
    bw->Latitude     =  Global_Var.Latitude;
    bw->Longitude    =  Global_Var.Longitude;
}

//需要保存的数据压入队列
void Push_RecordData(RecordFileData a)
{
    mtxRecordDatQueue.lock();
    RecordDatQueue.push(a);
    mtxRecordDatQueue.unlock();
}

//计算时间差，单位秒
int Diff_Dat(RecordFileData* a,RecordFileData* b)
{
    time_t t_a, t_b;
    struct tm mtm;
    // 设置 mtm 结构体
    mtm.tm_year =  a->DateTime[0]+100;
    mtm.tm_mon  =  a->DateTime[1] - 1; // 注意月份是从 0 开始的
    mtm.tm_mday =  a->DateTime[2];
    mtm.tm_hour =  a->DateTime[3];
    mtm.tm_min  =  a->DateTime[4];
    mtm.tm_sec  =  a->DateTime[5];
    mtm.tm_isdst = -1; // 不考虑夏令时
    t_a=mktime(&mtm);
    mtm.tm_year =  b->DateTime[0]+100;
    mtm.tm_mon  =  b->DateTime[1] - 1; // 注意月份是从 0 开始的
    mtm.tm_mday =  b->DateTime[2];
    mtm.tm_hour =  b->DateTime[3];
    mtm.tm_min  =  b->DateTime[4];
    mtm.tm_sec  =  b->DateTime[5];
    t_b=mktime(&mtm);
    int diff = difftime(t_b, t_a);
    return diff;
}

//确定结构体数据的状态是否变化
bool Cmp_dat(RecordFileData* a,RecordFileData* b)
{
    if(a->BDStat != b->BDStat)
         return true;
    if(a->ctrl_opt != b->ctrl_opt)
         return true;
    if(a->Net_Stat != b->Net_Stat)
          return true;
    if(a->end_p.PointID != b->end_p.PointID 
               || a->end_p.PointType != b->end_p.PointType
               || a->end_p.PointV != b->end_p.PointV)
          return true;
    if(a->EquipStatus != b->EquipStatus)
          return true;
    if(a->first_p.PointID != b->first_p.PointID 
                   || a->first_p.PointType != b->first_p.PointType
                   || a->first_p.PointV != b->first_p.PointV )
          return true;
    if(a->linedirect != b->linedirect)                               
          return true;
    if (strcmp((const char*)a->LineName, (const char*)b->LineName))
	  	  return true;
    if(a->MaxSpeed != b->MaxSpeed)
          return true;
    if(a->ctrl_opt != b->ctrl_opt)
          return true;
    if (strcmp((const char*)a->noteTxt, (const char*)b->noteTxt))
	  	  return true;
    if(a->TaxStat != b->TaxStat)
          return true;
    if(a->TrainState != b->TrainState)
          return true;
    if(a->voiceid != b->voiceid)
          return true;
    if (strcmp((const char*)a->voiceTxt, (const char*)b->voiceTxt))
	  	  return true;                      

    return false;
}

//建立数据记录文件
FILE *CreateDataFile()
{
    FILE *fp;
    char tempbuf[128];
    sprintf(tempbuf,"%srdat",Global_Var.cmd_path);
    DIR *dp;
    if ((dp = opendir(tempbuf)) == NULL)
    {
       mkdir(tempbuf, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
    }
    char FileName[512];
    struct tm *t;
    time_t tt;
    time(&tt);
    t=localtime(&tt);
    sprintf(FileName,"%s/lsp_dat_%d_%d_%4d%02d%02d%02d%02d%02d",tempbuf,
                                       Global_Var.TrainTypeID,Global_Var.TrainNumber,
                                       t->tm_year+1900,t->tm_mon+1,t->tm_mday,
                                       t->tm_hour,t->tm_min,t->tm_sec);     
    fp=fopen(FileName,"wb");
    RecordFileHead bwh;

    bwh.TLJ = Global_Var.TLJID;
    bwh.StationID = Global_Var.StationID;
    bwh.CheCi=Global_Var.CheCi;
    bwh.TrainNum = Global_Var.TrainNum;
    bwh.TrainTypeID = Global_Var.TrainTypeID;
    bwh.TrainNumber = Global_Var.TrainNumber;
    bwh.DriverNo  = Global_Var.DriverNo;
    bwh.CopilotNo = Global_Var.CopilotNo; 
    bwh.Version   =10;
    bwh.ZVersion  = Global_Var.ZVersion;
    bwh.MVersion  =Global_Var.MVersion; 
    bwh.CreatDate[0]=t->tm_year-100;
    bwh.CreatDate[1]=t->tm_mon+1;
    bwh.CreatDate[2]=t->tm_mday;
    bwh.CreatDate[3]=t->tm_hour;
    bwh.CreatDate[4]=t->tm_min;
    bwh.CreatDate[5]=t->tm_sec;
    fwrite(&bwh,sizeof(bwh),1,fp);
    fflush(fp);
    return fp;
} 


//保存数据线程
void* Thread_SaveData(void *lpPara)
{
    FILE *fp;
    bool open_stat=false;
    RecordFileData pre_dat,cur_dat;
    long t0;
    t0=what_time_is_it_sec();
    uint16_t pre_StationID;
    while(1)
    {
      mtxRecordDatQueue.lock();
      if(RecordDatQueue.empty()){
          mtxRecordDatQueue.unlock();
          //连续10分钟没有数据，关闭文件
          if(open_stat && (what_time_is_it_sec()-t0)>10*60){
                 fclose(fp);
                 open_stat=false;
          }
          continue;
      }
      pre_dat=cur_dat;
      cur_dat=RecordDatQueue.front();
      RecordDatQueue.pop();
      mtxRecordDatQueue.unlock();
	  if(  Cmp_dat(&cur_dat, &pre_dat) ||
              (Diff_Dat(&cur_dat, &pre_dat)>2 && cur_dat.Speed>0)) {//状态发生变化

		  if(!open_stat){//建立记录文件
                   fp=CreateDataFile(); 
                   pre_StationID=Global_Var.StationID;
                   if(fp!=NULL){
                      open_stat=true;
                   }
              }
              //站场变化，关闭已有文件，重新建立文件
              if(pre_StationID!=Global_Var.StationID && open_stat){
                   fclose(fp);
                   fp=CreateDataFile(); 
                   pre_StationID=Global_Var.StationID;
              }
              fwrite(&cur_dat,sizeof(cur_dat),1,fp);
              fflush(fp);
              t0=what_time_is_it_sec();
	  }
    }
}

