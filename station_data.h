#pragma once
#include "TaxData.h"

typedef unsigned short ushort;
typedef unsigned char byte;

#pragma pack(push)

#pragma pack(1)

typedef struct _HeadPos {
	unsigned char flg;       //数据有效 0 无效 1 有效
	unsigned char headType;  //火车端头  A端   B端
	unsigned char bRunHead;  //是否是前进车头 0 不是 1 是
	double hx;               //火车端头垂坐标
	double hy;               //火车端头垂坐标
	_HeadPos() {
		flg = 0;
		headType = 0;
		bRunHead = 0;
		hx = 0;
		hy = 0;
	}
public:
	_HeadPos &operator=(const _HeadPos &temp) {
		if (this == &temp) {
			return *this;
		}
		flg = temp.flg;
		headType = temp.headType;
		hx = temp.hx;
		hy = temp.hy;
		return *this;
	}
}HeadPos;

typedef struct _TrainSendInfo  //向地面传送的机车实时信息数据结构
{
	ushort          header;                  //头信息    
	ushort          length;                  //报文长度
	ushort          signtype;                //信息类别编码  1;
	byte            StationID;               //站场ID   
	byte            PlineID;                 //当前线路编号
	byte            HeadType;                //A节或B节  0--A  1--B
	byte            TAXStat;                 //TAX状态
	byte            GPSStat;                 //GPS状态
	double          tx;                      //当前火车WGS84坐标
	double          ty;                      //当前火车WGS84坐标
	double          longitude;               //当前火车经度
	double          latitude;                //当前火车纬度 
	byte            direction;               //火车方向 1上行 2下行
	byte            PointType;               //前方防撞点类型 ，0 没有  1--信号灯  2--接触网终点 3--脱轨器  4--土挡 5--站界
	byte            PointID;                 //防撞点全局索引
	float           DistoP;                  //距离当前防撞点距离
	TAXTIME         TAXTime;		         //TAX时间
	ushort          Speed;	                 //机车速度
	ushort          MaxSpeed;                //机车限速
	unsigned int    DriverNo;	             //司机号
	unsigned int    EngineNo;	             //机车号
	byte            EngineType;              //机车型号
	float           train_direct;            //火车方向角度
	HeadPos         headPos;                 //车头标志
	///add new ////
	char            LocoState;               //调车状态   0 非调车，1 调车
	char            LampStateValue;          //信号灯状态  1：蓝灯，2：白灯，3红灯，初始值0
	///add new ////
	char            EngineTypeStr[20];       //机车型号
	char            TaxIsExit;               //TAX是否存在
	char            State4G;                 //4G状态
	double          LinePer;                 //火车在当前线路的百分比
	char            LineName[20];            //当前线路名字
	/** add 20210118 **/
	char            ProVersion;              //协议版本 ,起始为3，之后累加
	char            LampName[20];            //灯名字
	ushort          winver;                  //显示屏软件版本
	ushort          acver;                   //rk3399或tx2主机版本
	ushort          lampver;                 //信号灯识别软件版本
	float           gps_err;                 //定位误差   
	unsigned char   TrainType;               //车次字母部分
	unsigned int    TrainNum;                //车次
	char            yuliu[23];               //预留
	/** add 20210118 **/
	byte            sc;                      //校验和 
	
}TrainSendInfo;

typedef struct _TrSentData {
	TrainSendInfo tinfo;
	unsigned char tdata_stat;
	TAXDATA taxdata;
}TrSentData;
#pragma pack(pop)
