#include "stdio.h"
#include "string.h" 
#include <vector>
#include "station_data.h"
using namespace std;
vector<TrSentData> v_sentbuf;
//获取测试数据
void GetTestData(char *dat_file)
{
   FILE* fp;
   TrSentData mdata;
   fp = fopen(dat_file, "rb"); 
   unsigned int d_ver;
   unsigned char ch;
   unsigned char tbuf[5120];
   fread(&d_ver, sizeof(d_ver), 1, fp);
   int i=0,j=0;
   int rstat = 0;
		for (i = 0;; i++) {
			if (feof(fp)) break;
			fread(&mdata.tinfo, sizeof(TrainSendInfo), 1, fp);
			fread(&mdata.tdata_stat, 1, 1, fp);
			rstat = mdata.tdata_stat;
			//rstat = 0;
			while(rstat) {
				if (feof(fp)) {
					if(d_ver==10){
						memcpy(&mdata.taxdata, &tbuf[0], sizeof(TAXDATA));
						break;
					}
					else if (d_ver == 11) {
						char tax_buf[256];
						memcpy(&tax_buf[0], &tbuf[0], 72);
						//AnTax((unsigned char*)&tax_buf[0], &mdata.taxdata);
						break;
					}
				}
				fread(&ch, 1, 1, fp);
				tbuf[j++] = ch;
				if(d_ver==10){
					if (j > 1 && tbuf[j - 2] == 0xbb && tbuf[j - 1] == 0x55) {
						memcpy(&mdata.taxdata, &tbuf[0],sizeof(TAXDATA));
						j = 0;
						long fpos=ftell(fp);
						fseek(fp, fpos - 2, 0);
						rstat = 0;
						break;
					}
				}
				else if (d_ver == 11) {
					char tax_buf[256];
					if (j > 1 && tbuf[j - 2] == 0xbb && tbuf[j - 1] == 0x55) {
						memcpy(&tax_buf[0], &tbuf[0], 72);
						//AnTax((unsigned char *) & tax_buf[0], &mdata.taxdata);
						j = 0;
						long fpos = ftell(fp);
						fseek(fp, fpos - 2, 0);
						rstat = 0;
						break;
					}
				}
			}
			v_sentbuf.push_back(mdata);
		}   
}


