#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <queue>
#include <mutex>
using namespace std;

queue<ushort> Sign_gpio;
mutex mtxSign_gpio;

//GPIO Function
void executeCMD(const char* cmd, char* result)
{
    char buf_ps[1024] = {0};
    char ps[1024] = {0};
    FILE *fptr = NULL;
    strcpy(ps,cmd);
    if ((fptr = popen(ps,"r")) != NULL)
    {
        pclose(fptr);
        fptr =NULL;
    }else {
        printf("popen %s error\n",ps);
    }

}

void GPIO_ON() {
    FILE * fp  = NULL;
    fp = fopen("/sys/class/gpio/gpio36/value","w");
    fprintf(fp,"%d",1);
    fclose(fp);
    ////////////////////////////////////////////////////////////////////////
    //fp = fopen("/sys/class/gpio/gpio23/value","w");
    //fprintf(fp,"%d",1);
    //fclose(fp);
    ////////////////////////////////////////////////////////////////////////
    fp = fopen("/sys/class/gpio/gpio20/value","w");
    fprintf(fp,"%d",1);
    fclose(fp);

    fp = fopen("/sys/class/gpio/gpio88/value","w");
    fprintf(fp,"%d",0);
    fclose(fp);
    printf("gpio ON\n");

}

void GPIO_OFF() {
    FILE * fp  = NULL;
    fp = fopen("/sys/class/gpio/gpio36/value","w");
    fprintf(fp,"%d",0);
    fclose(fp);
    ////////////////////////////////////////////////////////////////////////
    //fp = fopen("/sys/class/gpio/gpio23/value","w");
    //fprintf(fp,"%d",0);
    //fclose(fp);
    ////////////////////////////////////////////////////////////////////////
    fp = fopen("/sys/class/gpio/gpio20/value","w");
    fprintf(fp,"%d",0);
    fclose(fp);

    fp = fopen("/sys/class/gpio/gpio88/value","w");
    fprintf(fp,"%d",0);
    fclose(fp);

    printf("gpio OFF\n");
}

void GPIOInit(){
    FILE *p=NULL;
    system("sudo chmod a+w /sys/class/leds/user-led0/brightness");
    p=fopen("/sys/class/leds/user-led0/brightness","w");
    fprintf(p,"%d",1);
    fclose(p);
    system("sudo chmod a+w /sys/class/leds/user-led1/brightness");//Tax Led
    p=fopen("/sys/class/leds/user-led1/brightness","w");
    fprintf(p,"%d",0);
    fclose(p);
    executeCMD("sudo chmod a+rw /sys/class/gpio/export",NULL);
    p = fopen("/sys/class/gpio/export","w");
    fprintf(p,"%d",92);//定位状态	绿色	搜索到卫星	常亮	GPIO2_D4
    fflush(p);
    fprintf(p,"%d",89);
    fflush(p);
    //////////////////////////////////////////////
//    fprintf(p,"%d",154);
//    fflush(p);
    //////////////////////////////////////////////
    fprintf(p,"%d",36);//控制车挡接口的GPIO A1
    fflush(p);
    //////////////////////////////////////////////
//    fprintf(p,"%d",23);//控制车挡接口的GPIO A2
//    fflush(p);
    //////////////////////////////////////////////
    fprintf(p,"%d",20);//控制车挡接口的GPIO B1
    fflush(p);
    fprintf(p,"%d",88);//控制车挡接口的GPIO B2
    fflush(p);





    executeCMD("sudo chmod a+rw /sys/class/gpio/export",NULL);
    p = fopen("/sys/class/gpio/export","w");
    fprintf(p,"%d",92);//定位状态	绿色	搜索到卫星	常亮	GPIO2_D4
    fflush(p);
    fprintf(p,"%d",89);
    fflush(p);
    //////////////////////////////////////////////
    fprintf(p,"%d",154);
    fflush(p);
    //////////////////////////////////////////////
    fprintf(p,"%d",36);//控制车挡接口的GPIO A1
    fflush(p);
    //////////////////////////////////////////////
    //fprintf(p,"%d",23);//控制车挡接口的GPIO A2
    //fflush(p);
    //////////////////////////////////////////////
    fprintf(p,"%d",20);//控制车挡接口的GPIO B1
    fflush(p);
    fprintf(p,"%d",88);//控制车挡接口的GPIO B2
    fflush(p);


    fclose(p);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio92/direction",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio92/value",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio89/direction",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio89/value",NULL);
    ////////////////////////////////////////////////////////////////////////
//    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio154/direction",NULL);
//    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio154/value",NULL);
    ////////////////////////////////////////////////////////////////////////
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio36/direction",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio36/value",NULL);
    ////////////////////////////////////////////////////////////////////////
    //executeCMD("sudo chmod a+rw /sys/class/gpio/gpio23/direction",NULL);
    //executeCMD("sudo chmod a+rw /sys/class/gpio/gpio23/value",NULL);
    ////////////////////////////////////////////////////////////////////////
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio20/direction",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio20/value",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio88/direction",NULL);
    executeCMD("sudo chmod a+rw /sys/class/gpio/gpio88/value",NULL);

    p = fopen("/sys/class/gpio/gpio92/direction","w");
    fprintf(p,"out");
    fclose(p);
    p = fopen("/sys/class/gpio/gpio89/direction","w");
    fprintf(p,"out");
    fclose(p);
    ////////////////////////////////////////////////////////////////////////
//    p = fopen("/sys/class/gpio/gpio154/direction","w");
//    fprintf(p,"out");
//    fclose(p);
    ////////////////////////////////////////////////////////////////////////
    p = fopen("/sys/class/gpio/gpio36/direction","w");
    fprintf(p,"out");
    fclose(p);
    ////////////////////////////////////////////////////////////////////////
    //p = fopen("/sys/class/gpio/gpio23/direction","w");
    //fprintf(p,"out");
    //fclose(p);
    ////////////////////////////////////////////////////////////////////////
    p = fopen("/sys/class/gpio/gpio20/direction","w");
    fprintf(p,"out");
    fclose(p);
    p = fopen("/sys/class/gpio/gpio88/direction","w");
    fprintf(p,"out");
    fclose(p);

    p = fopen("/sys/class/gpio/gpio92/value","w");
    fprintf(p,"%d",0);
    fclose(p);
    p = fopen("/sys/class/gpio/gpio89/value","w");
    fprintf(p,"%d",1);
    fclose(p);
    ////////////////////////////////////////////////////////////////////////
//    p = fopen("/sys/class/gpio/gpio154/value","w");
//    fprintf(p,"%d",1);
//    fclose(p);
    ///////////////    //fprintf(p,"%d",154);
    //fflush(p);/////////////////////////////////////////////////////////
    printf("GPIO init\n");
    GPIO_OFF();
}

//产生GPIO脉冲
void GPIOPulse(ushort width)
{
    mtxSign_gpio.lock();
    Sign_gpio.push(width);
    mtxSign_gpio.unlock();
}


void *Thread_GPIOMon(void *pvoid)
{
   while(1){
        mtxSign_gpio.lock();
        if(!Sign_gpio.empty()){
            ushort width =Sign_gpio.front();
            GPIO_ON();
            usleep(width);
            GPIO_OFF();
            Sign_gpio.pop();
        }
   }
}
