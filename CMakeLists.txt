cmake_minimum_required(VERSION 3.10)

project(DS_ACServer)

message("DS ACServer")

if (CMAKE_SYSTEM_PROCESSOR MATCHES "x86_64")
    set(ARCH_DIR x86)
else()
    set(ARCH_DIR arm)
endif()

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS}")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")


include_directories(
    inc
)
link_directories(
   lib
)

set(link_libs  m dl pthread z)

add_executable(ACServer         main.cpp
                                gps.cpp
                                crc.cpp
                                md5.cpp
                                sm4.cpp 
                                gpio.cpp 
                                wdog.cpp 
                                n_tax.cpp
                                ntrip.cpp
                                ds_lsp.cpp
                                readcfg.cpp
                                checkip.cpp
                                tinyxml2.cpp
                                rk_filelog.cpp
                                map_update.cpp
                                test_dat.cpp  
                                save_data.cpp  
                                udp_to_server.cpp
                                
                )

target_link_libraries(ACServer ${link_libs})


