#include "ntrip.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h>

#include "gps.h"
#include <pthread.h>
#include "tx2_filelog.h"

int socketfd;
struct sockaddr_in sockaddr_zys;
char n_usr[30] = "";
char n_passwd[30] = "";
char n_mountp[20] = "";
bool NtripFlag = false;

extern char GGABuf[256];
extern int GGALen;


int base64Encode(char* result,char const* origSigned, unsigned origLength)
{  
  static const char base64Char[] = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"; 
  unsigned char const* orig = (unsigned char const*)origSigned;  
  if (orig == NULL) return NULL;  
  
  unsigned const numOrig24BitValues = origLength/3;  
  bool havePadding = origLength > numOrig24BitValues*3;  
  bool havePadding2 = origLength == numOrig24BitValues*3 + 2;  
  unsigned const numResultBytes = 4*(numOrig24BitValues + havePadding);  
  //char* result = new char[numResultBytes+1];
  
  // Map each full group of 3 input bytes into 4 output base-64 characters:  
  unsigned i;  
  for (i = 0; i < numOrig24BitValues; ++i)   
  {  
    result[4*i+0] = base64Char[(orig[3*i]>>2)&0x3F];  
    result[4*i+1] = base64Char[(((orig[3*i]&0x3)<<4) | (orig[3*i+1]>>4))&0x3F];  
    result[4*i+2] = base64Char[(((orig[3*i+1]&0x0f)<<2) | (orig[3*i+2]>>6))&0x3F];  
    result[4*i+3] = base64Char[(orig[3*i+2]&0x3f)&0x3F];  
  }  
  
  // Now, take padding into account.  (Note: i == numOrig24BitValues)  
  if (havePadding)   
  {  
    result[4*i+0] = base64Char[(orig[3*i]>>2)&0x3F];  
    if (havePadding2)  
    {  
      result[4*i+1] = base64Char[(((orig[3*i]&0x3)<<4) | (orig[3*i+1]>>4))&0x3F];  
      result[4*i+2] = base64Char[((orig[3*i+1]&0x0f)<<2)&0x3F];  
    }   
    else   
    {  
      result[4*i+1] = base64Char[((orig[3*i]&0x3)<<4)&0x3F];  
      result[4*i+2] = '=';  
    }  
    result[4*i+3] = '=';  
  }  
  
  result[numResultBytes] = '/0';  
  return numResultBytes;
}

int NtripRecv(int fd,char* buf,int len,int sec)
{
	int fs_sel;  
    fd_set fs_read;  
     
    struct timeval time;  
     
    FD_ZERO(&fs_read);  
    FD_SET(fd,&fs_read);  
     
    time.tv_sec = sec;  
    time.tv_usec = 0;  
          
    fs_sel = select(fd+1,&fs_read,NULL,NULL,&time);     
    if(fs_sel)  
	{  
		len = recv(fd,buf,len,0);		  
		return len;  
	}  
    else  
	{  
		printf("recv failed!\n");  
		return -1;  
	}  
}

int NtripInit(char* ip,int port,char* usr,char* passwd,char* mountp)
{
	
    memset(&sockaddr_zys,0,sizeof(sockaddr_zys));
    sockaddr_zys.sin_family = AF_INET;
    inet_pton(AF_INET,ip,&sockaddr_zys.sin_addr);
    sockaddr_zys.sin_port = htons(port);

	strcpy(n_usr,usr);
	strcpy(n_passwd,passwd);
	strcpy(n_mountp,mountp);
	return 0;
}

int NtripCon()
{
	socketfd = socket(AF_INET,SOCK_STREAM,0);
    if((connect(socketfd,(struct sockaddr*)&sockaddr_zys,sizeof(sockaddr_zys)))<0)
	{
		printf("ntrip connect error!\n");
		return -1;
	}

	char buf[1024] = "";
	int len = sprintf(buf,
		"GET /%s HTTP/1.0\r\n"
		"User-Agent: NTRIP NtripTest/1.0.0\r\n"
		"Accept: */*\r\n"
		"Connection: close\r\n"
		"Authorization: Basic ",n_mountp);

    char tmp[256] = "";
    sprintf(tmp,"%s:%s",n_usr,n_passwd);
    len += base64Encode(buf+len,tmp,strlen(tmp));
	buf[len++] = '\r';
	buf[len++] = '\n'; 
	buf[len++] = '\r';  
	buf[len++] = '\n';

    //printf("send: %s",buf);
	if(send(socketfd, buf, (size_t)len, 0) != len)  
	{          
		printf("send error\n");     
		return -1;       
	}

	memset(buf,0,sizeof(buf));
	int recvLen = 0;
	if((recvLen = NtripRecv(socketfd, buf, sizeof(buf)-1,3)) > 0 && strstr(buf, "200 OK"))
	{
		printf("recv server ok\n");
		//send(socketfd, "", 0, 0);
		return 0;
	}
	else
	{
		printf("recv server error\n");
		return -1;
	}
	
}

void * Thread_GGASend(void *lpPara)
{
	while(1)
	{
		if(NtripFlag && GGALen > 0)
		{
			
			int sendlen = 0;				
			if((sendlen = send(socketfd, GGABuf, GGALen, 0)) <= 0)  
			{          
				printf("send error\n"); 				      
			}
			else
			{
				//printf("send gpgga len = %d\n%s",sendlen,GGABuf);
			}
				
		}

		sleep(10);
	}
}

void * Thread_NTRIP(void *lpPara)
{	
	sleep(3);
	int comfd=*(int *)lpPara;
	pthread_t GGASendTid;
	pthread_create(&GGASendTid,NULL,Thread_GGASend,NULL);
	
	while(1)
	{
		if(NtripCon() != -1)
		{
			NtripFlag = true;
			char buf[1024*10] = "";
			int recvLen = 0;
			while((recvLen = NtripRecv(socketfd, buf, sizeof(buf)-1,15)) > 0)
			{
				//printf("ntrip recv data len = %d\n",recvLen);
				if(recvLen > 100)
				{
					int res = SerialSend(comfd,buf,recvLen);
					if(res <= 0){
						printf("com write error!\n");
					}		
			
				}				
				memset(buf,0,sizeof(buf));
			}

			NtripFlag = false;
			close(socketfd);
			socketfd = -1;
        }else{
           rk_syslog("Ntrip Server Connect Fail.");
        }

		sleep(10);
	}
}
