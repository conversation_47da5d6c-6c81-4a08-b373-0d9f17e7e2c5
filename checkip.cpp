#include <iostream>
#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <string>
#include <sys/time.h>
#include <vector>
#include <unistd.h>
#include <string.h>
#include <dirent.h>
#include <sys/stat.h>
#include "math.h"
#include "tx2_filelog.h"
#include "readcfg.h"
#include "DS_Data.h"
#include "gps.h"

using namespace std;
int stat_3g=0;//   0无信号    5 信号最强

extern StaticVar Global_Var;


// 将信号质量值转换为 0 到 5 的范围
int convertSignalQuality(int csqValue) {
    if (csqValue == 0 || csqValue == 99) {
        return 0; // 没有信号或未知
    }
    // 映射到 0 到 5 的范围
	int rez=round((csqValue * 5.0) / 31.0);
    return rez;
}

// 解析 AT+CSQ 返回的信号质量值
int parseSignalQuality(char *response) {
    char *token;
    int rssi, quality;

    // 查找 "+CSQ:" 子字符串
    char *csq = strstr(response, "+CSQ:");
    if (csq != NULL) {
        // 跳过 "+CSQ:" 子字符串
        csq += 5;

        // 从 "+CSQ:" 后面的第一个字符开始解析
        token = strtok(csq, ",\r\n");
        if (token != NULL) {
            rssi = atoi(token);
            token = strtok(NULL, ",\r\n");
            if (token != NULL) {
                quality = atoi(token);
                //return quality;
            }
			return rssi;
        }
    }
    return -1; // 解析失败
}

int Check4GDev(char *devicename)
{
    FILE *pp;
    char cmdstr[500];
    sprintf(cmdstr,"nmcli device status |grep %s",devicename);
    pp=popen(cmdstr,"r");
    if(!pp){
       return 0;
    }
    char temp[512];
    int stat=0;
    for (int i = 0;; i++)
    {
        fgets(temp, 512, pp);
        if (feof(pp)) break;
        if(strstr(temp,"已连接")!=NULL){
            stat=1;
            break;
        }else if(strstr(temp,"已断开")!=NULL){
            stat=2;
            break;
        }else if(strstr(temp,"不可用")!=NULL){
            stat=3;
            break;
        }
    }
    fclose(pp);

    return stat;
}





bool checkNet(char *ip)
{
    /*
       -c 2（代表ping次数，ping 2次后结束ping操作） -w 2（代表超时时间，2秒后结束ping操作）
    */
   FILE *pp;
   char cmd_str[500];
   char temp[512];

   sprintf(cmd_str,"sudo ping %s  -c 2 -w 2",ip);
   pp=popen(cmd_str,"r");
   if(!pp){
      return 0;
   }

    //把文件一行一行读取放入vector
    string s;
    vector<string> v;
    sleep(5);
    while (1)
    {

        fgets(temp, 512, pp);
        if (feof(pp)) break;
        s=temp;
        v.push_back(s);
    }

    fclose(pp);

    if (v.size() > 1)
    {
        string data = v[v.size()-2];
        int iPos = data.find("received,");
        if (iPos != -1 )
        {
            data = data.substr(iPos+10,3);//截取字符串返回packet loss
            int  n = atoi(data.c_str());
            if(n == 0)
             return 1;
            else
            return 0 ;
        }

    }else{
        return 0;
    }

}

bool check_led0()
{
     FILE *pp;
     pp=popen("ls -l /sys/class/leds |grep system_work_led0","r");
     if(!pp){
        return false;
     }
     char temp[512];
     int stat=0;
     for (int i = 0;; i++)
     {
         fgets(temp, 512, pp);
         if (feof(pp)) break;
         if(strstr(temp,"system_work_led0")!=NULL){
             stat=1;
             break;
         }
    }
    return stat;
}


bool check_led2()
{
     FILE *pp;
     pp=popen("ls -l /sys/class/leds |grep system_work_led2","r");
     if(!pp){
        return false;
     }
     char temp[512];
     int stat=0;
     for (int i = 0;; i++)
     {
         fgets(temp, 512, pp);
         if (feof(pp)) break;
         if(strstr(temp,"system_work_led2")!=NULL){
             stat=1;
             break;
         }
    }
    return stat;
}


bool check_led3()
{
     FILE *pp;
     pp=popen("ls -l /sys/class/leds |grep system_work_led3","r");
     if(!pp){
        return false;
     }
     char temp[512];
     int stat=0;
     for (int i = 0;; i++)
     {
         fgets(temp, 512, pp);
         if (feof(pp)) break;
         if(strstr(temp,"system_work_led3")!=NULL){
             stat=1;
             break;
         }
    }
    return stat;
}



//检查4G模块状态
void * Thread_Check3G(void *lpPara)
{
 int dev_stat=0;
 int comfd;//串口句柄
 const char *at_command = "AT+CSQ\r\n";
 char buffer[128];
 int bytes_read;
 FILE *p;

 comfd = SerialInit(Global_Var.RadioCom, 115200);
 while(1)
 {
        dev_stat =Check4GDev(Global_Var.DevName4G);
        if(dev_stat==1){//装置在线
            write(comfd, at_command, strlen(at_command));	      
	        sleep(1);
            bytes_read = read(comfd, buffer, sizeof(buffer) - 1);
            if (bytes_read > 0) {
  		        buffer[bytes_read]='\0';
		  		 // 解析信号质量
                 stat_3g = convertSignalQuality(parseSignalQuality(buffer));
            }else{
                 stat_3g=0;
                 rk_syslog("4G通信故障");     
            }
         }else{//装置离线
            stat_3g=0;
            rk_syslog("4G模块离线");
         }
         p=fopen("/sys/class/leds/user-led1/brightness","w");
         if(stat_3g>0){
            fprintf(p,"%d",1);
         }else
            fprintf(p,"%d",0);
         fclose(p);

        if(dev_stat==2){
               char cmdstr[500];
               sprintf(cmdstr,"sudo nmcli dev connect %s",Global_Var.DevName4G);
               system(cmdstr);
        }
        sleep(30);
 }

}


