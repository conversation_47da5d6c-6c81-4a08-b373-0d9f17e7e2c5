﻿#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <dirent.h>
#include <fcntl.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <sys/stat.h>
#include <netinet/in.h>
#include <queue>
#include <mutex>
#include "zlib.h"
#include "DS_Data.h"
#include "crc.h"
#include "readcfg.h"
#include "tx2_filelog.h"
#include "udp_to_server.h"
#include "md5.h"

#define MD5_SIZE		16
#define MD5_STR_LEN		(MD5_SIZE * 2)

using namespace std;

int idx_ver=0;//索引文件版本号i

extern int UServerSoc;
extern struct sockaddr_in ServerAddr;
extern StaticVar Global_Var;
extern queue<MapDataBuf> MapDatQueue;
extern mutex mtxMapDatQueue;


//加载站场图索引
bool LoadStation_idx()
{
    FILE* fp;
    char filename[512];
    sprintf(filename,"%sbwc_map/station.idx",Global_Var.cmd_path);

    long long cur_index = 0;
    if((fp = fopen(filename, "rb"))==NULL) return false;
    fseek(fp, 0, SEEK_END);//移到文件尾
    long long fileSize = ftell(fp);
    fseek(fp, 0, SEEK_SET);//移到文件头
    byte* fbuf = (byte*)malloc(fileSize);//文件字节数
    fread(fbuf, 1, fileSize, fp);
    fclose(fp);
    uint32_t crc = CRC32(fbuf, fileSize-4);
    uint32_t* oldcrc;
    oldcrc = (uint32_t*) (&fbuf[fileSize - 4]);
    if ( crc != *oldcrc) {
        return false;
    }
    idx_ver = 256*fbuf[0]+fbuf[1];
    uint16_t m_year;
    m_year = *(uint16_t*)&fbuf[2];
    byte  m_month,m_day,m_hour,m_min,m_sec;
    m_month = fbuf[4];
    m_day = fbuf[5];
    m_hour = fbuf[6];
    m_min = fbuf[7];
    m_sec = fbuf[8];
    Global_Var.Station_Num = *(uint16_t*)&fbuf[9];
    cur_index=15;
    Global_Var.StationS.clear();
    for(int sn=0;sn<Global_Var.Station_Num;sn++)
    {
       BWStation tempObj;
       tempObj.TLJ=fbuf[cur_index++];
       unsigned char nl=fbuf[cur_index++];
       for(int j=0;j<nl;j++){
         tempObj.TLJ_Name[j]=fbuf[cur_index++];
       }
       tempObj.TLJ_Name[nl]='\0';
       tempObj.StationID = *(uint16_t*)&fbuf[cur_index];
       cur_index=cur_index+2;
       nl=fbuf[cur_index++];
       for(int j=0;j<nl;j++){
         tempObj.Station_Name[j]=fbuf[cur_index++];
       }
       tempObj.Station_Name[nl]='\0';
       tempObj.pyPoints_N = *(uint16_t*)&fbuf[cur_index];
       cur_index=cur_index+2;
       for(int j=0;j<tempObj.pyPoints_N;j++){
          int xx=*(int *)&fbuf[cur_index]; 
          float mtx= xx/10000000.0;
          cur_index=cur_index+4;
          int yy =*(int *)&fbuf[cur_index];
          float mty=yy/10000000.0;
          cur_index=cur_index+4;
          tempObj.vertx.push_back(mtx);
          tempObj.verty.push_back(mty);
       }
       Global_Var.StationS.push_back(tempObj);
     }
     free(fbuf);
     return true;
}


//计算字符串md5码
void Get_Str_md5(byte *str, uint32_t len, char *md5_str)
{
   unsigned char md5_value[MD5_SIZE];
   MD5_CTX md5;
   MD5Init(&md5);
   MD5Update(&md5, str, len);
   MD5Final(&md5, md5_value);
   for(int i = 0; i < MD5_SIZE; i++)
   {
       snprintf(md5_str + i*2, 2+1, "%02x", md5_value[i]);
   }
   md5_str[MD5_STR_LEN] = '\0'; // add end

   return;
}

//发报文查询站场图版本
bool AskifMapNeedUpdate(byte TLJ,uint16_t Station_ID, uint32_t Version)
{
   bool stat=false;
   MapUpateRequest tempBW;
   tempBW.DataType=0x17;   //0x17报文
   time_t         nowT;
   struct tm        *t;
   time(&nowT);
   t=localtime(&nowT);
   tempBW.MsgID = nowT;
   tempBW.Year  = t->tm_year+1900;
   tempBW.Month = t->tm_mon+1;
   tempBW.Day   = t->tm_mday;
   tempBW.Hour  = t->tm_hour;
   tempBW.Min   = t->tm_min;
   tempBW.Sec   = t->tm_sec;
   tempBW.TLJ = TLJ;
   tempBW.StationID = Station_ID;
   tempBW.Version = Version;
   int res = SendDataToSer((byte *)&tempBW,sizeof(tempBW),0);
   if(res>sizeof(tempBW)) stat=true;
   return stat;
}
//获取站场图版本信息包
int GetAnsofServer(MapVersionAns *CurrAns)
{
  int stat=-1;
  int windex=0;
  MapVersionAns *tempAns;
  while(MapDatQueue.empty()){
      usleep(500*1000);
      if(windex++>4) break;
  }
  if(MapDatQueue.empty()) return -1;

  while(!MapDatQueue.empty()){
      mtxMapDatQueue.lock();
      MapDataBuf ansbuf=MapDatQueue.front();
      MapDatQueue.pop();
      mtxMapDatQueue.unlock();
      if(ansbuf.buf[0]==0x88){
          stat=1;
          tempAns=(MapVersionAns*)&ansbuf.buf[0];
          *CurrAns=*tempAns;
          break;
      }
  }
  return stat;
}

//确认站场图需要更新
bool MapUpdateACK(MapVersionAns *CurrAns)
{
  bool stat=false;
  MapVersionAns lspAns;
  lspAns.DataType=0x18; //0x18报文
  time_t         nowT;
  struct tm        *t;
  time(&nowT);
  t=localtime(&nowT);
  lspAns.MsgID = nowT;
  lspAns.Year  = t->tm_year+1900;
  lspAns.Month = t->tm_mon+1;
  lspAns.Day   = t->tm_mday;
  lspAns.Hour  = t->tm_hour;
  lspAns.Min   = t->tm_min;
  lspAns.Sec   = t->tm_sec;
  lspAns.TLJ   = CurrAns->TLJ;
  lspAns.StationID = CurrAns->StationID;
  strcpy(lspAns.FileName,CurrAns->FileName);
  lspAns.FileSize = CurrAns->FileSize;
  lspAns.FilePackN = CurrAns->FilePackN;
  strcpy(lspAns.MD5,CurrAns->MD5);
  int res = SendDataToSer((byte *)&lspAns,sizeof(lspAns),0);
  if(res>sizeof(lspAns)) stat=true;
  return stat;
}

//获取站场图数据
int GetMapData(byte *mapbuf,int b_pos,uint32_t *CurPackN)
{
    MapDatBWHead *tempHead;
    int totalsize=0;
    int windex=0;
    while(MapDatQueue.size()==0){
        //sleep(1);
        usleep(500*1000);
        if(windex++>10) break;
    }
    if(MapDatQueue.size()==0) 
       return -1;
    while(!MapDatQueue.empty()){
        mtxMapDatQueue.lock();
        MapDataBuf ansbuf=MapDatQueue.front();
        MapDatQueue.pop();
        mtxMapDatQueue.unlock();
        if(ansbuf.buf[0]==0x89){
            tempHead=(MapDatBWHead*)&ansbuf.buf[0];
            int bwsize=tempHead->Length - sizeof(MapDatBWHead);
            totalsize=totalsize+bwsize;
            byte ztest;
            for(int i=0;i<bwsize;i++){
              mapbuf[b_pos+i]=ansbuf.buf[sizeof(MapDatBWHead) + i];
            }
            *CurPackN=tempHead->CurPackN;
            b_pos=b_pos+bwsize;
        }
    }
    return totalsize;
}

//收到站场数据包后回传确认报文
bool MDataACK(uint32_t CurPackN,char TLJ,uint16_t StationID,uint32_t FilePackN)
{
   bool stat=false;
   MapDatACK tempACK;
   time_t         nowT;
   struct tm        *t;
   time(&nowT);
   t=localtime(&nowT);
   tempACK.DataType=0x19;
   tempACK.Length = sizeof(tempACK);
   tempACK.Version=10;
   tempACK.MsgID = nowT;
   tempACK.Year  = t->tm_year+1900;
   tempACK.Month = t->tm_mon+1;
   tempACK.Day   = t->tm_mday;
   tempACK.Hour  = t->tm_hour;
   tempACK.Min   = t->tm_min;
   tempACK.Sec   = t->tm_sec;
   tempACK.CurPackN=CurPackN;
   tempACK.TLJ = TLJ;
   tempACK.StationID = StationID;
   tempACK.FilePackN = FilePackN;
   int res = SendDataToSer((byte *)&tempACK,sizeof(tempACK),0);
   if(res>sizeof(tempACK)) stat=true;
   return stat;
}



//获取最新版站场地图
int GetNewMap(byte TLJ, uint16_t  StationID,MapVersionAns *CurrAns)
{
   int loop_index=0;
   bool mACK =false;
   long t1,t2;
   t1= what_time_is_it_now();
   while(1)
   {
      if(MapUpdateACK(CurrAns)){
         mACK=true;
         break;
      }
      if(loop_index++>20) break;
      usleep(500*1000);
   }
   if(!mACK) 
      return -1;
   byte *databuf = (byte *)malloc(CurrAns->FileSize);
   int pos =0;
   int curSize=0;
   int totalSize =0;
   uint32_t CurPackID;
   char fname[256],fname_tmp[256],fname_bak[256];
   while(1)
   {
      curSize = GetMapData(databuf,totalSize, &CurPackID);
      if(curSize==-1){
          t2= what_time_is_it_now();
          if((t2-t1)>1200000){ //超过20分钟没有收到数据，退出
                return -1;    
          }
          MDataACK(CurPackID,CurrAns->TLJ,CurrAns->StationID,CurrAns->FilePackN);
          continue;
      }
     
      totalSize=totalSize+curSize;
      if(CurPackID==CurrAns->FilePackN) break;
      MDataACK(CurPackID,CurrAns->TLJ,CurrAns->StationID,CurrAns->FilePackN);
      //sleep(1);
   }
   char bufmd5[MD5_STR_LEN + 1];
   if (TLJ==0 && StationID==0){//站场图索引文件
           sprintf(fname,"%sbwc_map/station.idx",Global_Var.cmd_path);
   }else{//站场图文件
           sprintf(fname,"%sbwc_map/%d/%d/%d.map",Global_Var.cmd_path,TLJ,StationID,StationID);
   }
   if(totalSize == CurrAns->FileSize){//对比md5码
       Get_Str_md5(databuf, totalSize, bufmd5);
       if(strcmp(bufmd5,CurrAns->MD5) !=0)  
           return -1;       

       if(access(fname, F_OK) == 0) { //文件存在
           sprintf(fname_tmp,"%s.tmp",fname);
           sprintf(fname_bak,"%s.bak",fname);
           unlink(fname_tmp);        
           FILE *fp;
           fp=fopen(fname_tmp,"wb");
           fwrite(databuf,totalSize,1,fp);
           fclose(fp);
           unlink(fname_bak);        
           rename(fname, fname_bak);
           rename(fname_tmp, fname);
       }else{//文件不存在，直接写
            if(TLJ!=0 && StationID!=0){
                char dname[128];
                sprintf(dname,"%sbwc_map/%d",Global_Var.cmd_path,TLJ);
                DIR *dp;
                if ((dp = opendir(dname)) == NULL)
                {
                        mkdir(dname, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
                }
                sprintf(dname,"%sbwc_map/%d/%d",Global_Var.cmd_path,TLJ,StationID);
                if ((dp = opendir(dname)) == NULL)
                {
                        mkdir(dname, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
                }
            }

            FILE *fp;
            fp=fopen(fname,"wb");
            fwrite(databuf,totalSize,1,fp);
            fclose(fp);
       }
       free(databuf);
       if (TLJ==0 && StationID==0){
           LoadStation_idx();
       }
       char str[512];
       sprintf(str,"%s 升级成功.",fname);
       rk_syslog(str);  

   }else{
       char str[512];
       sprintf(str,"%s 升级失败,MD5错误.",fname);
       rk_syslog(str);  
       return -1;
   }
   return totalSize;
}

uint32_t GetMapVersion(byte TLJ,uint16_t StationID)
{
   uint32_t ver=0;
   u_char BigV,SmallV;
   char fname[256];
   FILE *fp;
   sprintf(fname,"%sbwc_map/%d/%d/%d.map",Global_Var.cmd_path,TLJ,StationID,StationID);
   if(access(fname, F_OK) == 0) { //文件存在
      fp=fopen(fname,"rb");
      fread(&BigV,1,1,fp);
      fread(&SmallV,1,1,fp);
      fclose(fp);
      ver=256*BigV+SmallV;
   }
   return ver;
}

//检查站场图是否需要更新
bool CheckandGetStationMap(byte TLJ,uint16_t Station_ID, uint32_t Version)
{
    int tempIndex=0;
    while(!AskifMapNeedUpdate(TLJ, Station_ID, Version)){
        if(tempIndex++>20) return false;
        usleep(500*1000);
    }
    MapVersionAns CurrAns;
    tempIndex=0;
    while(GetAnsofServer(&CurrAns)==-1) {
      AskifMapNeedUpdate(TLJ, Station_ID, Version);  
      if(tempIndex++>4){  
          char str[512];
          sprintf(str,"询问服务器 %d-%d 站场图的版本信息包失败.",TLJ,Station_ID);
          rk_syslog(str); 
          return false;
       }
    }

    if(CurrAns.TLJ == TLJ && CurrAns.StationID ==Station_ID
                          && CurrAns.Version !=Version)
    {
       if(GetNewMap(TLJ, Station_ID,&CurrAns)>0)
           return true;
        else{
           char str[512];
           sprintf(str,"升级 %d-%d 站场图失败.",TLJ,Station_ID);
           rk_syslog(str); 
           return false;
        }
    }

    return false;
}


//地图自动更新线程
void * Thread_MapUpdate(void *lpPara)
{
  char fname[512]; 
  FILE *fp;
  sprintf(fname,"%sbwc_map",Global_Var.cmd_path);
  DIR *dp;
  if ((dp = opendir(fname)) == NULL)
  {
       mkdir(fname, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
  }
  sprintf(fname,"%sbwc_map/station.idx",Global_Var.cmd_path);
  if (access(fname, F_OK) == 0) { //文件存在
      LoadStation_idx();
  }

  uint32_t m_ver=0;
  byte  tid;
  uint16_t sid;
  while(1){
     CheckandGetStationMap(0,0,idx_ver); //检查站场图索引文件     
     for(int i=0;i<Global_Var.StationS.size();i++)
     {
        tid=Global_Var.StationS[i].TLJ;
        sid=Global_Var.StationS[i].StationID;
        m_ver=GetMapVersion(tid,sid);
        CheckandGetStationMap(tid,sid,m_ver); //检查站场图索引文件     
        //sleep(1);
     }
     sleep(3600);
  }

}
