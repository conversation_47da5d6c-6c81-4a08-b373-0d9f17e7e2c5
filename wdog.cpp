/*
 *      看门狗封装
 *      注：只能开一条狗
 *
 */
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <iostream>

#include "string.h"

#ifndef WATCHDOG_H
#define WATCHDOG_H
//看门狗
#include "fcntl.h"
#include "unistd.h"
#include "stdio.h"
#include "sys/stat.h"
#include "syslog.h"
#include "errno.h"
#include "tx2_filelog.h"

using namespace std;
//看门狗
int fd_watchdog=88888888;

//开狗
bool open_dog()
{
    if(fd_watchdog != 88888888)
    {
        return 0;
    }
    system("sudo chmod a+w /dev/watchdog");
    fd_watchdog= open("/dev/watchdog",O_WRONLY);
    if(fd_watchdog == -1)
    {
        int err = errno;
        printf("\n!!! FAILED to open /dev/watchdog,errno:%d,%s\n",err,strerror(err));
        syslog(LOG_WARNING,"FAILED to open /dev/watchdog,errno:%d,%s\n",err,strerror(err));
        fd_watchdog=88888888;
        return 0;
    }
    else
    {
        return 1;
    }
}

//喂狗
bool feed_dog()
{
    if(fd_watchdog==88888888)
    {
        cout<<"没有狗"<<endl;
        return 0;
    }
    if(fd_watchdog >=0 )
    {
        static unsigned char food = 0;
        ssize_t eaten = write(fd_watchdog,&food,1);
        if(eaten != 1)
        {
            //puts("\n!!! FAILED feeding watchdog");
            //syslog(LOG_WARNING,"\n!!! FAILED feeding watchdog");
            return 0;
        }
        else
        {
            //cout<<"喂狗"<<endl;
            return 1;
        }
    }
    return 0;
}

//关狗
bool close_dog()
{
    if(close(fd_watchdog)==-1)
    {
        return 0;
    }
    fd_watchdog=88888888;
    return 1;
}

#endif // WATCHDOG_H




//开启看门狗
void * Thread_wdog(void *lpPara)
{

      //开启看门狗     //thl+
  if(open_dog())
  {
     //开启成功
     rk_syslog("成功开启看门狗");
  }
  else
  {
     //开启失败
      rk_syslog("开启看门狗失败");

  }
  while(1)
  {

     if(feed_dog())
     {
         //喂狗成功
     }
     else
     {
          //喂狗失败
         rk_syslog("喂狗失败.");

     }
     sleep(20);//30秒喂狗   60秒重启
  }

}




