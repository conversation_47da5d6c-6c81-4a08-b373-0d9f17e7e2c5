#ifndef DS_DATA_H
#define DS_DATA_H

#include <string.h>
#include <vector>
#include <mutex>

#pragma pack(push)
#pragma pack(1)
typedef unsigned char byte;

using namespace std;
typedef struct _BASEMSG   //基础信息包 0x01
{
	byte           DataType;		//0x01
	uint16_t         Length;		//信息包长度
	byte          FactoryID;		//厂家编号
	uint32_t      TimeStamp;		//发送数据时的时间戳(s)
	char     TrainNumber[8];		//列车编号
	char             Status;		//0：本务机，1：轨道车
	byte       IDentity[32];		//身份ID（CPU-UID）
	char                TLJ;		//所属局码
	uint16_t      Sectionno;		//所属段码
	uint32_t          Space;		//预留

	_BASEMSG(){
		memset(this,0,sizeof(_BASEMSG));

		this->DataType = 0x01;
		this->Length = sizeof(_BASEMSG); 
        this->FactoryID = 65;
	}
}BASEMSG;

typedef struct _FKPoint
{
    char       PointType;           //防控项点类型    // 1 信号灯  2 接触网终点标  3 脱轨器  4 土挡
                                                    // 5 站界    6 道岔     7 无网线路 8 特殊防控项点 9 禁停区
    uint16_t     PointID;           //防控项点编号
    char   PointName[12];           //防控项点名称
    uint32_t      PointV;           //防控项点状态
    uint32_t    PointDis;           //防控项点距离
//    _FKPoint(){
//        memset(this,0,sizeof(_FKPoint));
//    }
}FKPoint;

typedef struct _BDSMSG  //北斗解析信息包 0x02
{
	byte          DataType;			//0x02
	uint16_t        Length;			//信息包长度
    byte             Space1;		//预留（信号机切换）
	int          Longitude;			//经度（度数*10000000）
	int           Latitude;			//纬度（度数*10000000）
	short            Speed;			//精确到公里的速度
    uint64_t         MSGID;			//消息包ID  主机电脑时间毫秒数
	byte              Flag;			//原始定位标志
	uint16_t      BaseStID;		    //使用差分基站
	uint16_t    BaseStTime;		    //使用差分基站时效
	char           StarNum;			//卫星数
    //uint32_t         Space2;      //预留
    uint16_t        TypeID;		    //预留用作车型代码
	uint16_t        TrainN;		    //预留用作车号

	_BDSMSG(){
		memset(this,0,sizeof(_BDSMSG));
		this->DataType = 0x02;
		this->Length = sizeof(_BDSMSG);	
	}

}BDSMSG;

typedef struct _TAXMSG  //机车状态信息包 基于tax箱产生 0x03
{
	byte         DataType;			//0x03
	uint16_t       Length;			//信息包长度
	uint16_t    MAOuttime;			//MA时效性
	char     TrainOuttime;		    //车载数据时效性
	char         IsCanUse;			//LKJ数据是否可用1：可用，其他不可用
	uint16_t        Speed;			//机车速度
	char      TrainDirect;			//车载方向，1向前，16向后，
	char   TrainCondition;		    //对应位为“1”表示有效 D3:牵引 D2:向前 D1向后 D0:零位 (机车工况)
	char      EquipStatus;			//D7:通常模式 D6:调车模式  D4:降级模式 D2:非本务模式 (监控状态状态）
	char             KHBB;
	char   TrainNumber[8];		    //4 车次字母部分 4 车次数字部分
	char      ErrorNumber;			//b1b0: 1与TAX通讯是否故障 b3b2: 1BDS故障 
								    //b5b4: 1剩余MA报警，未越信号机；2剩余MA报警，越过信号机
	uint32_t     UseMSGID;			//当前使用的服务器MSGid
	int          Distance;			//运行距离
	short       DistanceA;		    //天线距离I端长度 cm
	short       DistanceB;		    //天线距离II端长度 cm
	
	short    TrainSignDis;          //车载主机计算距离前方信号机距离
	short   TrainCheckDis;			//车载主机计算矫正距离
	char    TrainOwnCheck;			//工况自检
	char    DriverNotrain;		    //司机确认前方无车/推车
								    //0：未确认
								    //1:20s内司机确认无车
								    //2:20s内司机确认推车
	char         space[4];			//预留
                                    //暂定space[0]用于记录事件类型 
                                    //       0 无
                                    //       1 第1次15km/h控速
                                    //       2 第1次15km/h控速7秒后第2次控停
                                    //       3 第1次控停 
                                    //       4 第2次控停 
                                    
	_TAXMSG(){
		memset(this,0,sizeof(_TAXMSG));
		this->DataType = 0x03;
		this->Length = sizeof(_TAXMSG);
	}

}TAXMSG;



typedef struct _DCHERouteInfoHead   //调车进路信息包包头
{
	byte         DataType;			//0x83
	uint16_t       Length;			//信息包长度
	int           Version;          //数据版本
    uint16_t         Year;          //服务器时间
    byte            Month;
    byte              Day;
    byte             Hour;
    byte              Min;
    byte              Sec;
	char              TLJ;          //局码
    uint16_t    StationID;          //站码
    char         Space[4];  	    //预留
    _DCHERouteInfoHead(){
		memset(this,0,sizeof(_DCHERouteInfoHead));
		this->DataType = 0x83;
	}	
}DCHERouteInfoHead;



typedef struct _DCHEGouardInfoHead   //调车防护信息包包头
{
    byte         DataType;			//0x84
	uint16_t       Length;			//信息包长度
    uint64_t        MSGID;          //对应接受车载发送包中消息包ID
	int          ServerId;          //服务器ID
	char  PushTrainmsg[4];          //B1，B2：预留    B3：是否推车 (0:否，1：推车)  B4：space
	char              TLJ;          //局码 
    uint16_t    StationID;          //站码
    uint16_t      Version; 	        //版本
	uint16_t  VersionNext; 	        //下一站版本
    char         Space[3];  	    //预留
	char         TDCSStat;          //站场是否有TDCS 1：有TDCS站 2：无TDCS站  其他：未知
	char          MonStat;          //监控模式, 回填车载03包中监控状态，用于判断非调车、非调防模式下不使用该MA信息 
	char       RoutePrecs;          //进路精准度,  1：通过北斗精准计算,  2：距离推算  其他：未知 
	char               xb;          //上下行（0：上行，1：下行，其他不确定）前方第一架信号机属性判断
	char           Direct;          //进路方向（1向前，16向后，其他不确定）回填车载方向	
    
	
    _DCHEGouardInfoHead(){
		memset(this,0,sizeof(_DCHEGouardInfoHead));
		this->DataType = 0x84;
		this->Version  =1;
	}	
}DCHEGouardInfoHead;




typedef struct _TRAINPOSInfo   //机车位置信息包 0x85
{
	byte         DataType;			//0x85
	uint16_t       Length;			//信息包长度
    uint64_t        MSGID;			//对应接受车载发送包中消息包ID
	int          ServerId;          //服务器ID
    char         MaStatus;          //服务器情况
	char         Space1[3];         //预留
	char              TLJ;          //局码
	uint16_t    StationID;          //站码
	uint32_t      Version;          //版本
	char       SignalType;          //前方防控项点类型
    uint16_t     SignalID;          //前方防控项点编号	
	uint16_t    SignalDis;          //前方防控项点距离	
	char           Space2;          //预留
	char               xb;          //上下行（0：上行，1：下行，其他不确定）前方第一架信号机属性判断
	char           Direct;          //进路方向（1向前，16向后，其他不确定）回填车载方向
	char            FlagB;          //标志：1 前方股道/道岔
	uint16_t     DataBLen;          //数据B长度
	int          CheckLen;          //服务器回填车载走行，车载校正
	char        Space3[4];          //预留
	char             NumB;          //包数 1有位置 ，0做心跳
	uint16_t    RailwayID;          //股道/道岔的编号
	char        Space4[4];          //预留
	char  RailwayName[30];          //股道/道岔的名称
	uint32_t      DisToDC;          //股道/道岔的距离（cm）         
	uint32_t   RailLength;          //股道/道岔的长度（cm）         
	uint16_t  RailwayStat;          //股道/道岔的状态值
                                    //用多个比特位的值描述状态时，值按高位在前、低位在后的原则计算得出。	
                                    //16bit（自定义），1表示机车运行方向 起点->终点  0 终点->起点
	uint16_t     MaxSpeed;			//股道/道岔的限速值					 
    _TRAINPOSInfo(){
        memset(this,0,sizeof(_TRAINPOSInfo));
        this->Length = sizeof(_TRAINPOSInfo);
		this->DataType = 0x85;
	}
}TRAINPOSInfo;


typedef struct _DCMonitorFHInfoHead   //主机到显示屏防护信息包包头
{
    byte         DataHead;			//0xFF
    byte         DataType;			//0x0A
    uint16_t       Length;			//信息包长度
    byte          Version;          //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    uint16_t        Speed;          //机车速度  单位公里每小时
    byte          DatStat;          //包状态   1: 表示在站场内
                                    //        0: 不在站场内，以下数据无意义。本包当心跳包使用
    byte      StationType;          //站场信号类型  0 无站场信号  1 TDCS  2 股道自动化
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    byte           BDStat;          //北斗状态  0:未定位 1:非差分定位 2:差分定位 3:无效GPS 4:固定 5:浮动 6:正在估算
    byte          TAXStat;          //TAX状态  1:正常   0:无数据
    byte          NetStat;          //网络状态  1:正常   0:故障
    byte           DCStat;          //是否调车  1:调车   0:非调车
    byte               xb;          //上下行    1:上行   0:下行

    _DCMonitorFHInfoHead(){
        memset(this,0,sizeof(_DCMonitorFHInfoHead));
        this->DataHead=0xFF;
        this->DataType=0x0A;
        this->Version =1;
    }

}DCMonitorFHInfoHead;

typedef struct _BWZJHeart           //车载主机到显示屏心跳包
{
    byte         DataHead;          //0xFF
    byte         DataType;			//0x0C
    uint16_t       Length;			//信息包长度
    byte            MsgID;          //消息ID，从0开始计数
    byte          TaxStat;          //Tax箱连接状态 1在线 0离线
    byte           BDStat;          //BDS状态, 0=未定位，1=非差分定位，2=差分定位，3=无效PPS，4=固定 5=浮动 6=正在估算
    byte             Db_v;          //通信信号质量好坏 0无 5最好
    byte           Instat;          //    0不在场内  1 进入站场
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte              crc;

    _BWZJHeart(){
        memset(this,0,sizeof(_BWZJHeart));
        this->DataHead=0xFF;
        this->DataType=0x0C;
        this->Length = 12;
    }
}BWZJHeart;


typedef struct _MapUpateRequest     //站场示意图更新数据请求包
{
    byte          DataType;			//0x17
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  主机电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    byte         space[4];          //备用

    _MapUpateRequest(){
       memset(this,0,sizeof(_MapUpateRequest));
       this->DataType = 0x17;
       this->Length = sizeof(_MapUpateRequest);
    }
}MapUpateRequest;

typedef struct _MapVersionAns     //站场示意图更新-版本信息包, 版本确认包
{
    byte          DataType;			//0x88  站场示意图更新-版本信息包
                                    //0x18  站场示意图更新-版本确认包
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    char     FileName[64];          //文件名
    uint32_t     FileSize;          //文件大小
    uint32_t    FilePackN;          //传输文件所需包数
    byte         space[4];          //备用
    char          MD5[33];          //文件MD5码
    _MapVersionAns(){
       memset(this,0,sizeof(_MapVersionAns));
       this->Length = sizeof(_MapVersionAns);
    }
}MapVersionAns;

typedef struct _MapDatBWHead     //站场示意图更新数据包包头
{
    byte          DataType;			//0x89 站场示意图更新数据包包头
    uint16_t        Length;			//信息包长度
    uint32_t         MSGID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码_
    uint16_t    StationID;          //站码
    uint32_t     CurPackN;          //当前数据包数
    uint32_t    FilePackN;          //传输文件所需包数
    uint16_t     FileSize;          //文件大小
    _MapDatBWHead(){
       memset(this,0,sizeof(_MapDatBWHead));
       this->DataType = 0x89;
    }
}MapDatBWHead;


typedef struct _MapDatACK     //站场示意图更新-数据确认包
{
    byte          DataType;			//0x19 站场示意图更新-数据确认包
    uint16_t        Length;			//信息包长度
    uint32_t         MsgID;			//消息包ID  电脑时间秒数
    uint32_t       Version;         //版本
    uint16_t         Year;          //年
    byte            Month;          //月
    byte              Day;          //日
    byte             Hour;          //时
    byte              Min;          //分
    byte              Sec;          //秒
    byte            yuliu;          //备用
    byte              TLJ;          //局码
    uint16_t    StationID;          //站码
    uint32_t     CurPackN;          //当前数据包数
    uint32_t    FilePackN;          //传输文件所需包数
    _MapDatACK(){
       memset(this,0,sizeof(_MapDatACK));
       this->DataType = 0x19;
    }
}MapDatACK;


typedef struct _RecordFileHead        //数据记录文件头结构
{
	ushort          header;                  //头信息 
    byte            TLJ;                     //局码
    ushort          StationID;               //站码
    uint16_t        TrainTypeID;             //车型代号
    uint16_t        TrainNumber;             //车号
    unsigned int    DriverNo;	             //司机号
	unsigned int    CopilotNo;	             //副司机号
    unsigned char   CheCi;                   //车次字母
	unsigned int    TrainNum;	             //车次
    char            TrainType;		         //0：本务机，1：轨道车
    uint32_t        Version;                 //数据版本
    uint32_t        ZVersion;                //主机软件版本
    uint32_t        MVersion;                //显示屏软件版本
    byte            CreatDate[6];            //建立文件时间 
                                             //年-2000[1]月[1]日[1]时[1]分[1]秒[1]  
    _RecordFileHead(){
       memset(this,0,sizeof(_RecordFileHead));
       this->header = 0x55AA;
    }
}RecordFileHead;

typedef struct _RecordFileData     //数据记录内容结构   
{
    ushort        header;              //头信息 
    byte          DateTime[6];         //发生时间 
                                       //年-2000[1]月[1]日[1]时[1]分[1]秒[1]  
    byte          TaxStat;             //Tax箱连接状态 1在线 0离线
    byte          BDStat;              //BDS状态, 0=未定位，1=非差分定位，2=差分定位，3=无效PPS，4=固定 5=浮动 6=正在估算
    byte          Net_Stat;            //通信信号质量好坏 0无 5最好                                         
	int           Longitude;		   //经度（度数*10000000）
	int           Latitude;			   //纬度（度数*10000000）
	short         Speed;			   //精确到公里的速度
    short         MaxSpeed;            //限速
    byte          linedirect;          //方向，0=未定，1=向前，2=向后  
    float	      LinePer;             //火车在当前线路的百分比
	char          LineName[20];        //当前线路名字
    FKPoint       first_p;             //前方防控项点
    FKPoint       end_p;               //终点防控项点
    byte          TrainState;          //机车工况   TaxBuf[43]
    char          EquipStatus;		   //装置状态   TaxBuf[69]
    char          ctrl_opt;            //控车操作
    char          voiceid;             //语音id   -1表示没有
    char          voiceTxt[32];        //非标语音文本
    char          noteTxt[32];         //提示文本 
    _RecordFileData(){
       memset(this,0,sizeof(_RecordFileData));
       this->header = 0x55BB;
       this->voiceid=-1;   
       strcpy(this->voiceTxt,"");
       strcpy(this->noteTxt,"");
    }
}RecordFileData;




#pragma pack(pop)


typedef struct _DSPoint
{
    float x;        //X 坐标
    float y;        //Y 坐标
    _DSPoint() {
        x = 0;
        y = 0;
    }
}DSPoint;

typedef struct _BWStation
{
    byte                      TLJ;       //局码
    uint16_t            StationID;       //站码
    char             TLJ_Name[20];       //铁路局
    char         Station_Name[30];       //站场名称
    uint16_t           pyPoints_N;       //站场多边形范围点数
    std::vector<float>      vertx;       //站场多边形范围边界点容器(经度)
	std::vector<float>      verty;       //站场多边形范围边界点容器(纬度)
//    _BWStation(){
//         memset(this,0,sizeof(_BWStation));
//    }
}BWStation;

typedef struct _Railway
{
	uint16_t    RailwayID;          //股道/道岔的编号
	char  RailwayName[30];          //股道/道岔的名称
	uint32_t      DisToDC;          //股道/道岔的距离（cm）         
	uint32_t   RailLength;          //股道/道岔的长度（cm）         
	uint16_t  RailwayStat;          //股道/道岔的状态值
	uint16_t     MaxSpeed;			//股道/道岔的限速值		
	char           direct;          //表示机车在此股道上的运行方向   1 起点->终点  0 终点->起点 其它不确定
	char               xb;          //上下行（0：上行，1：下行，其他不确定）
//	_Railway(){
//		memset(this,0,sizeof(_Railway));
//	}
}Railway;



//全局变量
typedef struct _StaticVar
{

    char                    *cmd_path;        //执行程序所在目录
    uint16_t              Station_Num;        //站场总数
    vector<BWStation>        StationS;        //站场容器
    int               preStationIndex;        //上一次站场图索引
    int               curStationIndex;        //当前站场图索引
    byte                        TLJID;        //局码
    char                  TLJName[20];        //铁路局名称
    uint16_t                StationID;        //站码
    char              stationName[30];        //站场名称
    byte                  StationStat;        //站场状态  0 无站场信号  1 TDCS   2 股道自动化
    byte                     GSM_Stat;        //移动通讯状态
    byte                     BDS_Stat;        //北斗定位状态 
                                              //定位状态 0=未定位，1=非差分定位，2=差分定位，3=无效PPS，4=固定 5=浮动 6=正在估算
    byte                     TAX_Stat;        //TAX状态  1正常  0 err
    byte                     Net_Stat;        //网络状态 通信信号质量好坏 0无 5最好      
    byte                      DS_Stat;        //地算服务器状态

	int                     Longitude;		  //经度（度数*10000000）
	int                      Latitude;		  //纬度（度数*10000000）    

    unsigned int             DriverNo;	      //司机号
	unsigned int            CopilotNo;	      //副司机号
    unsigned char               CheCi;        //车次字母
	unsigned int             TrainNum;	      //车次
    char                    TrainType;		  //0：本务机，1：轨道车

    uint16_t                    Speed;        //机车速度
    uint16_t              TrainTypeID;        //车型代号
    uint16_t              TrainNumber;        //车号
    byte                   TrainState;        //机车工况   TaxBuf[43]
    char                  EquipStatus;		  //装置状态   TaxBuf[69]
    uint32_t                      dis;        //车载计算的运行距离    

    uint64_t                TimeStamp;		  //对应接受车载发送包中消息包ID,电脑时间秒数(s)
    byte                     train_xb;        //当前机车上下行
    byte                   KeyPoint_N;        //防控项点数量
    mutex                     mutexKP;
    vector<FKPoint>         KeyPointS;        //防控项点容器
                                              //第1个为机车当前所面对的项点，余下为机车需要依次面对的项点
    byte                    KeyLine_N;        //进路股道数量
    mutex                     mutexKL;
    vector<Railway>          KeyLineS;        //进路股道容器
                                              //第1条为机车所在股道，余下为机车需要依次通过的股道

    uint32_t                 ZVersion;        //主机软件版本
    uint32_t                 MVersion;        //显示屏软件版本

    short                   DistanceA;		  //天线距离I端长度 cm
	short                   DistanceB;		  //天线距离II端长度 cm

    char                NtripUsr[128];        //差分服务器访问用户名
    int                       GPSPort;        //差分服务器端口
    char                    GPSIP[28];        //差分服务器IP 
    char                    SERIP[28];        //通信服务器IP
    int                       SERPort;        //通信服务器端口   
    char                    BDCom[30];        //北斗模块串口
    char                 PowerCom[30];        //智能电源串口
    char                   TaxCom[30];        //Tax箱串口 
    char                 RadioCom[30];        //4G模块串口 
    char                DevName4G[10];        //4G模块名称
    unsigned char        Train_ID[32];        //机车识别码


    _StaticVar(){
        this->preStationIndex=-1;
        this->curStationIndex=-1;
    }

}StaticVar;

//站场图数据缓存
typedef struct  _MapDataBuf
{
    uint16_t len;
    char  buf[2048];
}MapDataBuf;

#endif // DS_DATA_H