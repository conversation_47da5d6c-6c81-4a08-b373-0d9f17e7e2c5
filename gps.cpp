#include "gps.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <termios.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h>
#include "DS_Data.h"
#include "readcfg.h"
#include <ctime>
using namespace std;
char GGABuf[256] = "";
int GGALen = 0;

GPS_INFO GPSData;
bool GPSState = false;


extern StaticVar Global_Var;

//int comfd = -1;

extern StaticVar Global_Var;

bool check_led1()
{
     FILE *pp;
     pp=popen("ls -l /sys/class/leds |grep system_work_led1","r");
     if(!pp){
        return false;
     }
     char temp[512];
     int stat=0;
     for (int i = 0;; i++)
     {
         fgets(temp, 512, pp);
         if (feof(pp)) break;
         if(strstr(temp,"system_work_led1")!=NULL){
             stat=1;
             break;
         }
    }
    return stat;
}


int SerialInit(char* com,int baud)
{
    int comid=-1;
    memset((char*)&GPSData,0,sizeof(GPSData));
	
    comid = open(com,O_RDWR|O_NOCTTY);//|O_NDELAY
    if(comid < 0){
        printf("open com %s failed.\n",com);
		return -1;		
	}

	struct termios comios;
	if(tcgetattr(comid,&comios)!=0) 
    {
        printf("Setup serial %s -1.\n",com);
		return -1;
    }
	
	comios.c_iflag &= ~ (IXON | IXOFF | IXANY);
	comios.c_lflag &= ~ (ICANON | ECHO | ECHOE | ISIG);
	comios.c_iflag &= ~ (INLCR | ICRNL | IGNCR);
	comios.c_oflag &= ~(ONLCR | OCRNL);

	switch(baud)
	{
        case 9600:
            cfsetispeed(&comios, B9600);
            cfsetospeed(&comios, B9600);
            break;
        case 115200:
            cfsetispeed(&comios, B115200);
            cfsetospeed(&comios, B115200);
            break;
        case 38400:
            cfsetispeed(&comios, B38400);
            cfsetospeed(&comios, B38400);
            break;
        default:
            cfsetispeed(&comios, B9600);
            cfsetospeed(&comios, B9600);
    }
	
		
	comios.c_cflag &= ~PARENB;
	comios.c_cflag |=CS8;
	comios.c_cflag &= ~CSTOPB;/*默认为一位停止位； */

	comios.c_cc[VTIME] = 200;/*非规范模式读取时的超时时间；*/
    comios.c_cc[VMIN]  = 0; /*非规范模式读取时的最小字符数*/
    tcflush(comid ,TCIFLUSH);

    if((tcsetattr( comid, TCSANOW,&comios))!=0)
    {
        printf("com set error.\n");
        return -1;
    }
	//fcntl(comfd, F_SETFL, O_NONBLOCK);
    return comid;
}





int SerialSend(int comfd,char* buf,int len)
{
	return write(comfd,buf,len);
}

void * Thread_GPS(void *lpPara)
{
	char recbuf[1024] = "";

    FILE *fp;
	sleep(1);
	int comfd=*(int *)lpPara;
	while(1)
	{
		char ch = 0;
		read(comfd,&ch,1);
		if(ch == 0x24)
		{
			recbuf[0] = ch;
			unsigned char res = 1;
			do 
			{
				int readlen = read(comfd,&recbuf[res],1);
				if(readlen >= 0)
				{
					if (res ++ > 200)
					break;
				}
				
			} while (!(recbuf[res-1] == 0x0A && recbuf[res-2] == 0x0D));

			//printf("recv %d :%s .\n",res,recbuf);
			
            if(strstr(recbuf,"GNGGA"))
			{					
				memcpy(GGABuf,recbuf,res);
				GGALen = res;
				
				GPSState = true;
				char* pStr = &recbuf[6];
				char i = 0;
				while(1)
				{
					pStr = strchr(pStr+1,',');
					if (!pStr)
						break;
					if (++i == 5)
					{
						GPSData.State = atoi(pStr+1);
                        Global_Var.BDS_Stat = GPSData.State;
						if (GPSData.State == 0 || GPSData.State == 3)//未定位或者定位无效
							break;
					}
                    if (i == 6)//
                    {
                        GPSData.StarNum = atoi(pStr+1);
                    }
					if (i == 8)//海拔
					{
						//MainDlg->GPSData.height = strtol(pStr+1,NULL,10);
						GPSData.height = atoi(pStr+1);
						break;
					}
                    if (i == 10)
                    {
                        GPSData.BaseStationTime = atoi(pStr+1);
                    }
                    if (i == 12)
                    {
                        GPSData.BaseStationID = atoi(pStr+1);
                        break;
                    }
				}
			}
            else if(strstr(recbuf,"GNRMC"))
			{
				char* pStr = &recbuf[6];
				char i = 0;
				char buf[20] = "";

				//时间
				memcpy(buf,pStr+1,2);
				GPSData.GTime.hour = atoi(buf);
				memcpy(buf,pStr+1+2,2);
				GPSData.GTime.minute = atoi(buf);
				memcpy(buf,pStr+1+4,2);
				GPSData.GTime.second = atoi(buf);

				memset(buf,0,sizeof(buf));

				while(1)
				{
					pStr = strchr(pStr+1,',');
					if (!pStr)
						break;
					i++;					
					if (i == 1)
					{
						char state = atoi(pStr+1);
						if (state == 'V')//A-有效 V-无效
						{
							break;
						}
					}
					else if (i == 2)//纬度
					{
						memcpy(buf,pStr+1,strchr(pStr+1,',') - pStr -1);

						unsigned int La = atof(buf)/100;
                        double min = atof(buf) - La*100;
						min = min/60;

						GPSData.latitude = (La+min);
                        Global_Var.Latitude = GPSData.latitude*10000000;
						memset(buf,0,sizeof(buf));
					}
					else if (i == 4)//经度
					{
						memcpy(buf,pStr+1,strchr(pStr+1,',') - pStr -1);

						unsigned int Lo = atof(buf)/100;
                        double min = atof(buf) - Lo*100;
						min = min/60;

						GPSData.longitude = (Lo+min);
                        Global_Var.Longitude = GPSData.longitude*10000000;   
						memset(buf,0,sizeof(buf));
					}
					else if (i == 6)//速度
					{
						memcpy(buf,pStr+1,strchr(pStr+1,',') - pStr -1);

						GPSData.speed = atof(buf)*1.852;

						memset(buf,0,sizeof(buf));
					}
					else if (i == 8)//日期
					{
						memcpy(buf,pStr+1,2);
						GPSData.GTime.day = atoi(buf);
						
						memcpy(buf,pStr+1+2,2);
						GPSData.GTime.month = atoi(buf);
						memcpy(buf,pStr+1+4,2);
						GPSData.GTime.year = atoi(buf);
						memset(buf,0,sizeof(buf));
					}
				}

				//时间+8h
				struct tm tm;				
				tm.tm_year = GPSData.GTime.year + 2000 - 1900;
				tm.tm_mon = GPSData.GTime.month - 1;				
				tm.tm_mday = GPSData.GTime.day;
				tm.tm_hour = GPSData.GTime.hour;
				tm.tm_min = GPSData.GTime.minute;
				tm.tm_sec = GPSData.GTime.second;

				time_t timebuf = mktime(&tm);

				timebuf += 8*60*60;
				struct	tm time;
				localtime_r(&timebuf,&time);

				GPSData.GTime.year = time.tm_year + 1900 -2000;
				GPSData.GTime.month = time.tm_mon + 1;
				GPSData.GTime.day = time.tm_mday;
				GPSData.GTime.hour = time.tm_hour;
				GPSData.GTime.minute = time.tm_min;
				GPSData.GTime.second = time.tm_sec;

				/*
				//////测试显示
				char desBuf[200] = "";
				sprintf(desBuf,"time:%d-%d-%d %d:%d:%d \r\n longitude: %f\r\n latitude: %f\r\nheight: %d\r\nspeed: %d\r\nState: %d\r\n",GPSData.GTime.year,GPSData.GTime.month,GPSData.GTime.day,GPSData.GTime.hour,GPSData.GTime.minute,GPSData.GTime.second,
					GPSData.longitude,GPSData.latitude,GPSData.height,GPSData.speed,GPSData.State);
				printf("%s",desBuf);
				*/
				
			}
		}

        if(GPSState ==false || GPSData.State == 0 || GPSData.State == 3){
                fp = fopen("/sys/class/gpio/gpio89/value","w");
                fprintf(fp,"%d",0);
                fclose(fp);
        }else{
                fp = fopen("/sys/class/gpio/gpio89/value","w");
                fprintf(fp,"%d",1);
                fclose(fp);
        }
		memset(recbuf,0,sizeof(recbuf));
        usleep(5000);
        //usleep(100000);//100ms

	}
}





