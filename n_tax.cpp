extern "C"
{
    #include <stdio.h>
    #include <string.h>
    #include <unistd.h>
    #include <pthread.h>
    #include <signal.h>
    #include <fcntl.h>
    #include <termios.h>
    #include <linux/serial.h>
    #include <errno.h>
    #include <sys/ioctl.h>
    #include <arpa/inet.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include "TaxData.h"
}

#include "tx2_filelog.h"
#include "DS_Data.h"

typedef unsigned char UCHAR;
typedef unsigned int  UINT32;



static int speed_arr[] = {B38400, B19200, B9600, B4800, B2400, B1200, B300, B38400, B19200, B9600, B4800, B2400, B1200, B300};
static int name_arr[] = {38400, 19200, 9600, 4800, 2400, 1200, 300, 38400, 19200, 9600, 4800, 2400, 1200, 300};


extern char TaxCom[30];
TAXDATA curTaxData;
bool TaxStat=false;
unsigned short DataNum=0;
extern int TestState;
extern StaticVar Global_Var;

void request_send(int fd)
{
	int status;
	ioctl(fd, TIOCMGET, &status);
	status &= ~TIOCM_RTS;   // RTS ??????
	ioctl(fd, TIOCMSET, &status);
}

void clear_send(int fd)
{
	int status;
	ioctl(fd, TIOCMGET, &status);
	status |= TIOCM_RTS;    // RTS ??????
	ioctl(fd, TIOCMSET, &status);
}


void close_port(int fd)
{
	if(fd== 0)return;
	close(fd);
	fd = 0;
}

void set_speed(int fd, int speed)
{
	struct termios Opt;
	tcgetattr(fd, &Opt);
	for(int i = 0; i < sizeof(speed_arr)/sizeof(int); i++)
	{
		if(speed == name_arr[i])
		{
			tcflush(fd, TCIOFLUSH);
            cfsetispeed(&Opt, speed_arr[i]);
            //cfsetospeed(&Opt, speed_arr[i]);
			int status = tcsetattr(fd, TCSANOW, &Opt);
			if(status != 0) perror("tcsetattr fd1");
			return;
		}
		tcflush(fd,TCIOFLUSH);
    }
}

int set_parity(int fd, int databits, int stopbits, int parity)
{
	struct termios options;
	if  ( tcgetattr( fd,&options)  !=  0)
	{
		perror("SetupSerial 1");
		return(0);
	}

	options.c_cflag |= CLOCAL |CREAD ;  //CREAD???????  CLOCAL????????????????????
	options.c_cflag &= ~CSIZE;          //CSIZE ???锟斤拷 ?????锟斤拷???????????
	switch (databits)
	{
	case 7:
		options.c_cflag |= CS7;
		break;
	case 8:
		options.c_cflag |= CS8;
		break;
	default:
		fprintf(stderr,"Unsupported data size\n");
        return (0);void* Thread_TaxData(void* lParam);
	}
	switch (parity)
	{
	case 'n':
	case 'N':
		options.c_cflag &= ~PARENB;   //PARENB 锟斤拷??锟斤拷???
		options.c_iflag &= ~INPCK;    //INPCK 锟斤拷?????
		break;
	case 'o':
	case 'O':
		options.c_cflag |= (PARODD | PARENB);   //PARENB 锟斤拷??锟斤拷???  PARODD ?????锟斤拷??
		options.c_iflag |= INPCK;
		break;
	case 'e':
	case 'E':
		options.c_cflag |= PARENB;  //PARENB 锟斤拷??锟斤拷???
		options.c_cflag &= ~PARODD;   //INPCK 锟斤拷?????  PARODD ?????锟斤拷??
		options.c_iflag |= INPCK;
		break;
	case 'S':
	case 's':
		options.c_cflag &= ~PARENB;   //PARENB 锟斤拷??锟斤拷???
		options.c_cflag &= ~CSTOPB;   //2????锟斤拷
		break;
	case 'M':
	case 'm':
		options.c_cflag |= CMSPAR;   //PARENB 锟斤拷??锟斤拷???
		options.c_cflag &= ~PARODD;   //2????锟斤拷
		break;
		
	default:
		fprintf(stderr,"Unsupported parity\n");
		return (0);
	}

	switch (stopbits)
	{
	case 1:
		options.c_cflag &= ~CSTOPB;//1????锟斤拷
		break;
	case 2:
		options.c_cflag |= CSTOPB; //
		break;
	default:
		fprintf(stderr,"Unsupported stop bits\n");
		return (0);
	}

	/*No hardware control*/
	options.c_cflag &= ~CRTSCTS;
	/*No software control*/
	options.c_iflag &= ~(IXON | IXOFF | IXANY);
	/*delay time set */

	//options.c_cflag|= IXON|IXOFF|IXANY;   //  ????????????


	if(parity != 'n') options.c_iflag |= INPCK;
	options.c_cc[VTIME] = 150;
	options.c_cc[VMIN] = 0;

//	options.c_cc[VTIME] = 0;
//	options.c_cc[VMIN] = 14;
	/*raw model*/
	options.c_lflag  &= ~(ICANON | ECHO | ECHOE | ISIG);
	options.c_oflag  &= ~OPOST;

    options.c_iflag &= ~(INLCR|IGNCR|ICRNL);
	options.c_iflag &= ~(ONLCR|OCRNL);

	options.c_oflag &= ~(INLCR|IGNCR|ICRNL);
	options.c_oflag &= ~(ONLCR|OCRNL);

	tcflush(fd,TCIFLUSH);
	if (tcsetattr(fd,TCSANOW,&options) != 0)
	{
		perror("SetupSerial 3");
		return (0);
	}

	return (1);
}


// set baudrate to 28800, used for TAX 
int serial_set_speci_baud(int fd,int baud)
{
    #include <asm/termbits.h>
	struct termios2 tio;
	ioctl(fd, TCGETS2, &tio);
	tio.c_cflag &= ~CBAUD;
	tio.c_cflag |= BOTHER;
	tio.c_ispeed = baud;
	tio.c_ospeed = baud;
	/* do other miscellaneous setup options with the flags here */
	ioctl(fd, TCSETS2, &tio);

	return 0;

}

int uart_open(const char *dev,unsigned baud)
{
	int fd=0;
	//char * dev ="/dev/ttyAMA1";
	fd = open(dev, O_RDWR);

	if(-1 == fd)
	{
		fd = 0;
		printf("Can't Open Serial Port!\n");
        return -1;
	}
	else
	{
		printf("dev=%s\n",dev);
	}
	//if(baud==28800)
	serial_set_speci_baud(fd,baud);
	//else
	//	serial_set_speci_baud(fd,baud);//set_speed(fd, baud);
	
	set_parity(fd, 8, 1, 'N');

	return fd;
}


int AnalyseTax(unsigned char * szBuf,int len,TAXDATA *TaxData)
{
	unsigned char Tax40_Check,Tax32_Check,Tax72_Check;
	int i,k,m;
	i = 0,m = 0;
	Tax40_Check=0;
    for ( i = 0; i < len-72; i++ )
	{
        if (( szBuf[i] == 0x38 )&&( szBuf[i+32] == 0x39 ))//72字节
		{
			Tax32_Check = 0;
			k = 0;
			for ( k = 0; k < 31; k++ )
                Tax32_Check = Tax32_Check + szBuf[k + i];
			Tax32_Check = ~Tax32_Check + 1;
			Tax72_Check = 0;
			k = 0;
			for ( k = 0; k < 39; k++ )
                Tax72_Check = Tax72_Check + szBuf[k + i + 32];
			Tax72_Check = ~Tax72_Check + 1;
            if (( Tax32_Check == szBuf[i + 31] )&&( Tax72_Check == szBuf[i + 71] ))
			{
				//实际交路号
                TaxData->FactRoute = szBuf[15 + i];
                //阀缸压UCHAR AT1_checksc(const UINT32 inLength_U32, const UCHAR* inBuf_pU8) //计算报文校验和，返回值为校验和
                TaxData->ValvePressure = szBuf[17 + i]*256+szBuf[16 + i];
				//柴油机转速、原边电流。
                TaxData->DieselSpeedAndOriginalCurrent = szBuf[i + 20]*256+szBuf[i + 19];

				//车次字母
                TaxData->TrainType = szBuf[9 + i];
				//车次 
                TaxData->TrainNum=szBuf[30 + i] * 65536 + szBuf[29 + i]*256 +  szBuf[28 + i];
				//运行的时间
                TaxData->TAXTime.Second = szBuf[35 + i] & 0x3f;
                TaxData->TAXTime.Minute=(szBuf[36 + i] & 0x0f)*4 + ((szBuf[35 + i]&0xc0) >> 6);
                TaxData->TAXTime.Hour=(szBuf[37 + i] & 0x01)*16+ ((szBuf[36 + i] & 0xf0) >> 4);
                TaxData->TAXTime.Day=(szBuf[37 + i] & 0x3e)/2;
                TaxData->TAXTime.Month=(szBuf[38 + i] & 0x03)*4 + ((szBuf[37 + i] & 0xc0)>>6);
                TaxData->TAXTime.Year=((szBuf[38 + i]&0xfc)>>2)+2000;
				//速度、限速
                TaxData->Speed=(szBuf[40 + i] & 0x02)* 256 + szBuf[39 + i];
				
                TaxData->MaxSpeed = (szBuf[41 + i] & 0x0f)*64 + (szBuf[40 + i] >> 2);

				//机车信号	
                TaxData->TrainSign = szBuf[42 + i];
				//机车工况
                TaxData->TrainState = szBuf[43 + i];

				//信号机编号
                TaxData->SignNo = szBuf[45 + i]*256 + szBuf[44 + i];
				//信号机种类
                TaxData->SignType = szBuf[46 + i] & 0x07;
				//公里标
                TaxData->Signpost=(szBuf[49 + i]&0x003F)*65536 + szBuf[48 + i]* 256 +szBuf[47 + i ];
				//总重
                TaxData->CarWeight=szBuf[51 + i]*256 +szBuf[50 + i];
				//计长
                TaxData->CarLong=szBuf[53 + i]*256 + szBuf[52 + i];
				//辆数
                TaxData->CarCount=szBuf[54 + i];
				//客/货、本/补 标志
                TaxData->TrainFlag=szBuf[55 + i];
				//区段号
                TaxData->SectionNo=szBuf[58 + i];
				//车站号
                TaxData->StationNo=szBuf[59 + i];
				//司机号 
                TaxData->DriverNo=szBuf[10 + i]*65536 +szBuf[61 + i]*256 + szBuf[60 + i];
				//副司机号 
                TaxData->CopilotNo=szBuf[11 + i]*65536 +szBuf[63 + i]*256 +szBuf[62 + i];
				//机车号
                TaxData->EngineNo=szBuf[65 + i]*256 +szBuf[64 + i];
                //TaxData->EngineNo = 452;
				//机车型号
                TaxData->EngineType = (szBuf[14 + i]&0x3E)*256 + szBuf[66 + i];
                //TaxData->EngineType = 161;
                //设备位置 80void* Thread_TaxData(void* lParam); A节 40 B节
                TaxData->DevicePlace = szBuf[14 + i]&0xC0;
				//管压
                TaxData->PipePressure =(szBuf[68 + i] & 0x03) * 256 + szBuf[67 + i];
                //装置状态 b0:1/0 降级/监控 b2： 1/0 调车/非调车
                TaxData->stateDLoco = ((szBuf[69 + i]) & 0x04) >>2;
                TaxData->monStat = (szBuf[69 + i]) & 0x01;
				memset(TaxData->TAXDataBuf,0,sizeof(TaxData->TAXDataBuf));
				for(m=0;m<72;m++)
				{
                    TaxData->TAXDataBuf[m]=szBuf[i + m] ;
				}
				return 72;
			}
		} 
	}   
	i = 0;
    for ( i = 0; i < len-40; i++ )
	{
        if ( szBuf[i] == 0x39 )//40字节
		{

			Tax40_Check = 0;
			k = 0;
			for ( k = 0; k < 39; k++ )
                Tax40_Check = Tax40_Check + szBuf[k + i];
			Tax40_Check = ~Tax40_Check + 1;
            if ( Tax40_Check == szBuf[i + 39] )
			{
				//运行的时间
                TaxData->TAXTime.Second = szBuf[i + 3] & 0x3f;
                TaxData->TAXTime.Minute=(szBuf[i + 4] & 0x0f)*4 + ((szBuf[i + 3]&0xc0) >> 6);
                TaxData->TAXTime.Hour=(szBuf[i + 5] & 0x01)*16+ ((szBuf[i + 4] & 0xf0) >> 4);
                TaxData->TAXTime.Day=(szBuf[i + 5] & 0x3e)/2;
                TaxData->TAXTime.Month=(szBuf[i + 6] & 0x03)*4 + ((szBuf[i + 5] & 0xc0)>>6);
                TaxData->TAXTime.Year=((szBuf[i + 6]&0xfc)>>2)+2000;
				//速度、限速
                TaxData->Speed=(szBuf[i + 8] & 0x03)* 256 + szBuf[i + 7];
                TaxData->MaxSpeed = (szBuf[i + 9] & 0x0f)*64 + (szBuf[i + 8] >> 2);
                //信号机编号void* Thread_TaxData(void* lParam);
                TaxData->SignNo = szBuf[i + 13]*256 + szBuf[i + 12];
				//信号机种类
                TaxData->SignType = szBuf[i + 14] & 0x07;
				//公里标
                TaxData->Signpost=szBuf[i + 17]*65536 + szBuf[i + 16]* 256 +szBuf[i + 15];
				//总重
                TaxData->CarWeight=szBuf[i + 19]*256 +szBuf[i + 18];
				//计长
                TaxData->CarLong=szBuf[i + 21]*256 + szBuf[i + 20];
				//辆数
                TaxData->CarCount=szBuf[i + 22];
				//客/货、本/补 标志
                TaxData->TrainFlag=szBuf[i + 23];
				//车次
                TaxData->TrainNum=((szBuf[i + 23] & 0x40)  >> 6) * 65536 + szBuf[i + 25] * 256 + szBuf[i + 24];
				//区段号
                TaxData->SectionNo=szBuf[i + 26];
				//车站号
                TaxData->StationNo=szBuf[i + 27];
				//司机号
                TaxData->DriverNo=szBuf[i + 28]*256 + szBuf[i + 29];
				//副司机号
                TaxData->CopilotNo=szBuf[i + 30]*256 +szBuf[i + 31];
				//机车号
                TaxData->EngineNo=szBuf[i + 33]*256 +szBuf[i + 32];
				//机车型号
                TaxData->EngineType = szBuf[i + 34];
				//管压
                TaxData->PipePressure =(szBuf[i + 36] & 0x03) * 256 + szBuf[i + 35];
				memset(TaxData->TAXDataBuf,0,sizeof(TaxData->TAXDataBuf));
				for(m=0;m<40;m++)
				{
                    TaxData->TAXDataBuf[m]=szBuf[i + m] ;
				}	  
				return 40;
			}
		}

	}   
	return 0;
}

///获取tax箱数据
void* Thread_TaxData(void* lParam)
{

    int fd = 0;
    unsigned char DataBuf[720] = "";;
    TAXDATA TaxData;
    int TAX_TYPE;
    fd=uart_open(Global_Var.TaxCom,28800);
    int open_num=0;
    while(fd==-1)
    {
       fd=uart_open(Global_Var.TaxCom,28800);
       if(open_num++ >50) break;
       sleep(2);
    }
    if(fd==-1){
        rk_syslog("Tax模块串口打开失败.");
        return ((void*)0);
    }else{
        clear_send(fd);
        rk_syslog("Tax模块串口打开正常.");
    }

	while(1)
    {
            memset(&DataBuf,0x00,sizeof(DataBuf));
            //read(fd,DataBuf, 720);
            for(int i = 0;i < 144;i++)
            {
                read(fd,&DataBuf[i],1);
            }

            TAX_TYPE=AnalyseTax(DataBuf,144,&TaxData);

            if(TAX_TYPE>0)  {
               DataNum++;
               memcpy(&curTaxData,&TaxData,sizeof(TaxData));
			   Global_Var.TrainTypeID = curTaxData.EngineType;
			   Global_Var.TrainNumber = curTaxData.EngineNo;
			   Global_Var.CheCi = curTaxData.TrainType;
			   Global_Var.TrainNum = curTaxData.TrainNum;
			   Global_Var.DriverNo = curTaxData.DriverNo;
			   Global_Var.CopilotNo =curTaxData.CopilotNo; 
			   Global_Var.TrainState=curTaxData.TrainState;
			   Global_Var.EquipStatus =curTaxData.TAXDataBuf[69]; 
            }

	}

}
///检查与tax箱通讯状态
void* Thread_CheckTax(void* lParam)
{
  unsigned short n1=0,n2=0,n3=0;
  int start_i=0;
  bool pre_TaxStat=false;
  while(1)
   {
      n3=n2;
      n2=n1;
      n1=DataNum;
      if(start_i>5){
          if(n1!=n2){
              pre_TaxStat=TaxStat;
              TaxStat=true;
              Global_Var.TAX_Stat=1;
              if(TaxStat!=pre_TaxStat){
                   rk_syslog("开始接收Tax数据.");
              }

          }else{
              pre_TaxStat=TaxStat;
              TaxStat=false;
              Global_Var.TAX_Stat=0;
              if(TaxStat!=pre_TaxStat){
                  rk_syslog("TAX箱无数据.");
              }

          }
      }else{
         start_i=start_i+1;
      }
      sleep(2);
   }
}
///与tax箱通讯状态指示灯
void* Thread_TaxLed(void* lParam)
{
   FILE * fp  = NULL;
    while(1){

        fp = fopen("/sys/class/gpio/gpio92/value","w");
        if(TaxStat)
            fprintf(fp,"%d",1);
        else
            fprintf(fp,"%d",0);
        fclose(fp);
        usleep(200*1000);
        fp = fopen("/sys/class/gpio/gpio92/value","w");
        fprintf(fp,"%d",0);
        fclose(fp);
        usleep(200*1000);
    }
}

