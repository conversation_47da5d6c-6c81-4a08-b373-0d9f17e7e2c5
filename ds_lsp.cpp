#include "ds_lsp.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h>

#include "math.h"
#include "TaxData.h"
#include "gps.h"
#include "ntrip.h"
#include "checkip.h"
#include "tx2_filelog.h"

#include "DS_Data.h"
#include "readcfg.h"
#include "udp_to_server.h"
#include "ds_station.h"
#include "crc.h"

#include <queue>
#include <mutex>
#include <algorithm>

#define TESTStat  1
#define LOCO_DISTEN 2.2      // 机车到铁轨的距离值


using namespace std;

struct SelLine{
  int lineid;
  float ds;
  int footindex;
};


#if TESTStat
double tx_test,ty_test;
TrainSendInfo TrainInfo;
#endif

mutex mtxBDS_Que;
queue<DSPoint> BDS_Que; //最新的连续10包GPS坐标数据队列

mutex mtxLinePer_Que;
queue<float> LinePer_Que;

int train_line_direct[40] = { 0 };
float pre_LinePer[5]={0};

SelLine closeLine[3];   //保存求出的距离机车最近的3条股道
extern GPS_INFO GPSData;
extern bool GPSState;
extern bool NtripFlag;
extern int TestState;


extern StaticVar Global_Var;
extern TAXDATA curTaxData;
extern bool TaxStat;

long what_time_is_it_now();

void SendAInfotoMonitor(byte *buf,int len);

float GetDS(DSPoint p1, DSPoint p2)
{
    return (float)sqrt((p1.x-p2.x)*(p1.x-p2.x)+(p1.y-p2.y)*(p1.y-p2.y));
}

void SetTrainDirect(int value)
{
    for(int i=0;i<40;i++){
        train_line_direct[i]=value;
    }
}

void PutTrainDirDattoBuf(int direct) {
    for (int i = 29; i > 0; i--) {
        train_line_direct[i] = train_line_direct[i - 1];
    }
    train_line_direct[0] = direct;
}

bool check_if_ok() {
    if ((train_line_direct[0] == train_line_direct[1]) &&
        (train_line_direct[0] == train_line_direct[2]) &&
        (train_line_direct[0] == train_line_direct[3]) &&
        (train_line_direct[0] == train_line_direct[4]) &&
        (train_line_direct[0] != train_line_direct[5]))
        return true;
    else
        return false;
}


void SetLinePer(float per)
{
    for(int i=0;i<5;i++){
        pre_LinePer[i]=per;
    }
}

void PutLinePer(float per)
{
    for (int i = 4; i > 0; i--) {
        pre_LinePer[i] = pre_LinePer[i - 1];
    }
    pre_LinePer[0] = per;
}


void Check_Per_Direct()
{
  if ((pre_LinePer[0] - pre_LinePer[4]) > 0.5 ) {
        PutTrainDirDattoBuf(1);//面向终点
  }
  else if ((pre_LinePer[0] - pre_LinePer[4]) < -0.5 ) {
        PutTrainDirDattoBuf(2);//面向起点
  }
}


void GeodeticToCartesian(PCRDCARTESIAN &pcc, PCRDGEODETIC &pcg)
{
	double B;        //纬度度数
	double L;        //经度度数
    double L0;       //中央经线度数
	double l;        //L-L0
	double t;        //tanB
	double m;        //ltanB
	double N;        //卯酉圈曲率半径 
	double q2;
	double x;        //高斯平面纵坐标
	double y;        //高斯平面横坐标
	double s;        //赤道至纬度B的经线弧长
	double f;        //参考椭球体扁率
	double e2;        //椭球第一偏心率
	double a;        //参考椭球体长半轴
	//double b;        //参考椭球体短半轴
	double a1;
	double a2;
	double a3;
	double a4;
	double b1;
	double b2;
	double b3;
	double b4;
	double c0;
	double c1;
	double c2;
	double c3;
	int Datum = 84;        //投影基准面类型：北京54基准面为54，西安80基准面为80，WGS84基准面为84
	int prjno = 0;        //投影带号
	int zonewide = 3;
	double IPI = 0.0174532925199433333333;        //3.1415926535898/180.0
	B = pcg.latitude; //纬度
	L = pcg.longitude; //经度
	if (zonewide == 6)
	{
		prjno = (int)(L / zonewide) + 1;
        L0 = prjno*zonewide - 3;
	}
	else
	{
		prjno = (int)((L - 1.5) / 3) + 1;
		L0 = prjno * 3;
	}

	if (Datum == 54)
	{
		a = 6378245;
		f = 1 / 298.3;
	}
	else if (Datum == 84)
	{
		a = 6378137;
		f = 1 / 298.257223563;
	}

	if (L > 112.51 && L < 112.57) //针对太原北站增加
		L0 = 111;                 //针对太原北站增加 
	
	L0 = L0*IPI;
	L = L*IPI;
	B = B*IPI;

	e2 = 2 * f - f*f;//(a*a-b*b)/(a*a);
	l = L - L0;
	t = tan(B);
	m = l * cos(B);
	N = a / sqrt(1 - e2* sin(B) * sin(B));
	q2 = e2 / (1 - e2)* cos(B)* cos(B);
	a1 = 1 + (double)3 / 4 * e2 + (double)45 / 64 * e2*e2 + (double)175 / 256 * e2*e2*e2 + (double)11025 / 16384 * e2*e2*e2*e2 + (double)43659 / 65536 * e2*e2*e2*e2*e2;
	a2 = (double)3 / 4 * e2 + (double)15 / 16 * e2*e2 + (double)525 / 512 * e2*e2*e2 + (double)2205 / 2048 * e2*e2*e2*e2 + (double)72765 / 65536 * e2*e2*e2*e2*e2;
	a3 = (double)15 / 64 * e2*e2 + (double)105 / 256 * e2*e2*e2 + (double)2205 / 4096 * e2*e2*e2*e2 + (double)10359 / 16384 * e2*e2*e2*e2*e2;
	a4 = (double)35 / 512 * e2*e2*e2 + (double)315 / 2048 * e2*e2*e2*e2 + (double)31185 / 13072 * e2*e2*e2*e2*e2;
	b1 = a1*a*(1 - e2);
	b2 = (double)-1 / 2 * a2*a*(1 - e2);
	b3 = (double)1 / 4 * a3*a*(1 - e2);
	b4 = (double)-1 / 6 * a4*a*(1 - e2);
	c0 = b1;
	c1 = 2 * b2 + 4 * b3 + 6 * b4;
	c2 = -(8 * b3 + 32 * b4);
	c3 = 32 * b4;
	s = c0*B + cos(B)*(c1*sin(B) + c2*sin(B)*sin(B)*sin(B) + c3*sin(B)*sin(B)*sin(B)*sin(B)*sin(B));
	x = s + (double)1 / 2 * N*t*m*m + (double)1 / 24 * (5 - t*t + 9 * q2 + 4 * q2*q2)*N*t*m*m*m*m + (double)1 / 720 * (61 - 58 * t*t + t*t*t*t)*N*t*m*m*m*m*m*m;
	y = N*m + (double)1 / 6 * (1 - t*t + q2)*N*m*m*m + (double)1 / 120 * (5 - 18 * t*t + t*t*t*t - 14 * q2 - 58 * q2*t*t)*N*m*m*m*m*m;

	//y = y + 1000000 * prjno + 500000;
	//pcc.x = x;
	//pcc.y = y - 38000000;
	//pcc.z = pcg.height;

	y = y  + 500000;
	pcc.x = x;
	pcc.y = y;
	pcc.z = pcg.height;

}


char*  GetMapFileName(int stationID)
{
    char *fn="/home/<USER>/lxjwd.bds";
    return fn;
}

bool LoadStation_Map(int stationID, DStation* st_map)
{
#if TESTStat
  char *filename="/home/<USER>/xzb.bds";
#else
  char *filename;
  filename=GetMapFileName(stationID);
#endif
  FILE* fp;
  long long cur_index = 0;
  fp = fopen(filename, "rb");
  fseek(fp, 0, SEEK_END);//移到文件尾
  long long fileSize = ftell(fp);
  fseek(fp, 0, SEEK_SET);//移到文件头
  byte* fbuf = (byte*)malloc(fileSize);//文件字节数
  fread(fbuf, 1, fileSize, fp);
  uint32_t crc = CRC32(fbuf, fileSize-6);
  uint32_t* oldcrc;
  oldcrc = (uint32_t*) (&fbuf[fileSize - 4]);
  byte ch1 = fbuf[fileSize - 5];
  byte ch2 = fbuf[fileSize - 6];
  if ( ch1 !=0xFE || ch2!= 0xFE || crc != *oldcrc) {
      return false;
  }
  fclose(fp);

  USHORT map_v;
  map_v = *(USHORT*)&fbuf[0];

  st_map->areaID = fbuf[2];
  for (int i = 0; i < 20; i++) {
      st_map->areaName[i] = fbuf[3 + i];
  }
  st_map->stationID = *(USHORT*)&fbuf[23];
  for (int i = 0; i < 30; i++) {
      st_map->stationName[i] = fbuf[25 + i];
  }
  st_map->stationType = fbuf[55];
  st_map->rectArea.xmin = *(float*)&fbuf[56];
  st_map->rectArea.xmax = *(float*)&fbuf[60];
  st_map->rectArea.ymin = *(float*)&fbuf[64];
  st_map->rectArea.ymax = *(float*)&fbuf[68];
  st_map->pyPoints_N = *(USHORT*)&fbuf[72];
  cur_index = 74;
  DSPoint tempDSPt;
  for (int i = 0; i < st_map->pyPoints_N; i++) {
     tempDSPt.x = *(double*)&fbuf[cur_index];
     cur_index = cur_index + 8;
     tempDSPt.y = *(double*)&fbuf[cur_index];
     cur_index = cur_index + 8;
     st_map->pyPoints.push_back(tempDSPt);
  }

  while (1) {
      byte c = fbuf[cur_index];
      if ( c != 0xfe) {
          if (cur_index > fileSize - 2) break;
          cur_index = cur_index + 1;
          continue;
      }
      cur_index = cur_index + 2;
      switch (fbuf[cur_index - 1]) {
          case 0://道岔
          {
              USHORT obj_n;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Turnout_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n;ii++) {
                  DSTurnout temp_tnot;
                  temp_tnot.t_id = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int j = 0; j < 12; j++) {
                      temp_tnot.to_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  temp_tnot.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  temp_tnot.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  st_map->Turnouts.push_back(temp_tnot);
              }
              break;
          }
          case 1://信号灯
          {
              USHORT obj_n;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Lamp_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSLamp t_lamp;
                  t_lamp.lamp_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_lamp.lamptype = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_lamp.lamp_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  t_lamp.lampkind = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.lampleftRight = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.lampheight = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_lamp.actualPos.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_lamp.actualPos.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_lamp.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_lamp.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_lamp.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Lamps.push_back(t_lamp);
              }
              break;
          }
          case 2: //接触网终点标
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->PowerEnd_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSPowerEnd t_PoweED;
                  t_PoweED.PowerEnd_index= *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_PoweED.PowerEnd_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_PoweED.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_PoweED.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_PoweED.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Powerends.push_back(t_PoweED);
              }
              break;
          }
          case 3: //脱轨器
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Derailer_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSDerailer t_DSDe;
                  t_DSDe.Derailer_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_DSDe.Derailer_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_DSDe.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_DSDe.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_DSDe.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Derailers.push_back(t_DSDe);
              }
              break;
          }
          case 4: //土挡
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Stoppoint_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DStoppoint t_stop;
                  t_stop.s_id = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_stop.Stype = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_stop.Stoppoint_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  t_stop.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_stop.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_stop.line_pos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->Stoppoints.push_back(t_stop);
              }
              break;
          }
          case 5: //站界
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->StLimit_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DStationLimit t_stlim;
                  t_stlim.StLimit_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_stlim.StLimit_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_stlim.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_stlim.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_stlim.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->StLimits.push_back(t_stlim);
              }
              break;
          }
          case 6: //禁停区
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->NPA_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSNoParkingArea t_npa;
                  t_npa.NPA_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;

                  t_npa.begin_pos.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_npa.begin_pos.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_npa.begin_linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;

                  t_npa.end_pos.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_npa.end_pos.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_npa.end_linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;

                  st_map->NPAs.push_back(t_npa);
              }
              break;
          }
          case 7: //特殊防控项点
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->SpecPoint_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSpecialPoint t_sp;
                  t_sp.sp_index = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  t_sp.sp_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int j = 0; j < 12; j++) {
                      t_sp.SPName[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 12;
                  for (int j = 0; j < 50; j++) {
                      t_sp.speechtxt[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 50;
                  t_sp.position.x = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_sp.position.y = *(double*)&fbuf[cur_index];
                  cur_index = cur_index + 8;
                  t_sp.linepos = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  st_map->SpecPoints.push_back(t_sp);
              }
              break;
          }
          case 8: //线路
          {
              USHORT obj_n = 0;
              obj_n = *(USHORT*)&fbuf[cur_index];
              st_map->Rail_N = obj_n;
              cur_index = cur_index + 2;
              for (int ii = 0; ii < obj_n; ii++) {
                  DSPline t_rail;
                  t_rail.PlineID = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int j = 0; j < 20; j++) {
                      t_rail.line_name[j] = fbuf[cur_index + j];
                  }
                  cur_index = cur_index + 20;
                  t_rail.up_type = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_rail.NonPowerStat = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  t_rail.LineLength = *(float*)&fbuf[cur_index];
                  cur_index = cur_index + 4;
                  t_rail.preLineN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.preLineN; nn++) {
                      USHORT t_preid;
                      t_preid = *(USHORT*)&fbuf[cur_index];
                      t_rail.preLineIDs.push_back(t_preid);
                      cur_index = cur_index + 2;
                  }
                  t_rail.nextLineN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.nextLineN; nn++) {
                      USHORT t_nextid;
                      t_nextid = *(USHORT*)&fbuf[cur_index];
                      t_rail.nextLineIDs.push_back(t_nextid);
                      cur_index = cur_index + 2;
                  }
                  t_rail.totalPointsN = *(USHORT*)&fbuf[cur_index];
                  cur_index = cur_index + 2;
                  for (int nn = 0; nn < t_rail.totalPointsN; nn++) {
                      DSPoint t_point;
                      t_point.x= *(double*)&fbuf[cur_index];
                      cur_index = cur_index + 8;
                      t_point.y = *(double*)&fbuf[cur_index];
                      cur_index = cur_index + 8;
                      t_rail.Points.push_back(t_point);
                  }
                  t_rail.totalLampN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalLampN; nn++) {
                      USHORT t_lampid;
                      t_lampid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Lamps.push_back(t_lampid);
                  }
                  t_rail.totalTurnoutN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalTurnoutN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Turnouts.push_back(t_toid);
                  }
                  t_rail.totalPowerEndN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalPowerEndN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Powerends.push_back(t_toid);
                  }
                  t_rail.totalStoppointN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalStoppointN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Stoppoints.push_back(t_toid);
                  }
                  t_rail.totalDerailerN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalDerailerN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.Derailers.push_back(t_toid);
                  }
                  t_rail.totalStLimitN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalStLimitN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.StLimits.push_back(t_toid);
                  }
                  t_rail.totalNPAN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalNPAN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.NPAs.push_back(t_toid);
                  }
                  t_rail.totalSPN = *(byte*)&fbuf[cur_index];
                  cur_index = cur_index + 1;
                  for (int nn = 0; nn < t_rail.totalSPN; nn++) {
                      USHORT t_toid;
                      t_toid = *(USHORT*)&fbuf[cur_index];
                      cur_index = cur_index + 2;
                      t_rail.SpecPoints.push_back(t_toid);
                  }
                  st_map->Railways.push_back(t_rail);
              }
              break;
          }
          case 0xFE: //校验
          {
              free(fbuf);
              return true;
          }
          default:
              break;
      }
  }

  free(fbuf);
  return true;
}

//把最新的连续10包BDS坐标数据存入队列
void PushBDSQueue(DSPoint train_point)
{
    mtxBDS_Que.lock();
    BDS_Que.push(train_point);
    while(BDS_Que.size()>10){
        BDS_Que.pop();
    }
    mtxBDS_Que.unlock();
    return ;
}

//队列清零
void ClearBDSQueue() {
    mtxBDS_Que.lock();
    BDS_Que.empty();
    mtxBDS_Que.unlock();
}

int isbetween_d(float a, float b, float c)//双精度型 is c between a and b ?
{
    int stat = 0;
    if (a<=b && c >= a && c <= b)
        stat = 1;
    else if (a>=b && c >= b && c <= a)
        stat = 1;

    return stat;
}

//点(x,y)距(x1,y1)和(x2,y2)两点连线的距离
//直线方程ax+bx+c=0;
float find_d_PointtoLine(float x1, float y1, float x2, float y2, float x, float y,
                          float *tx_adjuest_lo, float *ty_adjuest_lo)
{
    float k, a, b, c, d;
    if (x2 != x1) {
        k = (y2 - y1) / (x2 - x1);
        a = k; b = -1; c = y1 - k * x1;
        d = abs((a * x + b * y + c) / sqrt(a * a + b * b));
        //if (d < LOCO_DISTEN ) {//求机车的调整位置，垂足点
            *tx_adjuest_lo = (b * b * x - a * b * y - a * c) / (a * a + b * b);
            *ty_adjuest_lo = (-a * b * x + a * a * y - b * c) / (a * a + b * b);
        //}
    }
    else {
        d = abs(x - x1);
        *tx_adjuest_lo = x1;
        *ty_adjuest_lo = y;
    }

    return d;
}


//计算点到多线段的距离; -1表示点不在多线段上
float find_d_toMulPline(DStation curStation,int PlineID, float x, float y, int *footindex,
                                      float *tx_adjuest_dl, float *ty_adjuest_dl)
{
    float x1, y1, x2, y2;
    float d = -1;
    float d_min = 100000;
    int i_min = 0;
    float adx,ady;
    for (int i = 1; i < curStation.Railways[PlineID].totalPointsN; i++)
    {
        x1 = curStation.Railways[PlineID].Points[i - 1].x;
        y1 = curStation.Railways[PlineID].Points[i - 1].y;
        x2 = curStation.Railways[PlineID].Points[i].x;
        y2 = curStation.Railways[PlineID].Points[i].y;

        if (abs(x1-x2)>=abs(y1-y2)) {
            if (isbetween_d(x1, x2, x)) {
                d = find_d_PointtoLine(x1, y1, x2, y2, x, y, &adx, &ady);
                if (d < d_min) {
                    d_min = d;
                    i_min = i;
                    *tx_adjuest_dl = adx;
                    *ty_adjuest_dl = ady;
                }
            }
        }
        else {
            if (isbetween_d(y1, y2, y)) {
                d = find_d_PointtoLine(x1, y1, x2, y2, x, y, &adx, &ady);
                if (d < d_min) {
                    d_min = d;
                    i_min = i;
                    *tx_adjuest_dl = adx;
                    *ty_adjuest_dl = ady;
                }
            }
        }
    }
    if (i_min > 0) {
        *footindex = i_min-1;
    }
    if (d_min < 10000) d = d_min;
    return d;
}


//获取离机车最近的铁道，返回轨道编号
int GetTheClosestLineToTheTrain(DStation curStation,DSPoint curTrainPos,float* min_ds_1, int* cur_foot_index,
                                 float *tx_adjuest_dl, float *ty_adjuest_dl)
{
    float ds;
    float min_ds = 10000;
    int min_i = -1;
    float adx,ady;


    for(int i=0;i<3;i++){
        closeLine[i].ds=10000;
        closeLine[i].footindex=-1;
        closeLine[i].lineid =-1;
    }


        for (int i = 0; i < curStation.Rail_N; i++)
        {
            int tempfoot;

            ds = find_d_toMulPline(curStation, curStation.Railways[i].PlineID,curTrainPos.x, curTrainPos.y,
                                                    &tempfoot, &adx, &ady);

            if (ds >= 0 && ds <= min_ds) {
                closeLine[2] = closeLine[1];
                closeLine[1] = closeLine[0];
                closeLine[0].lineid=curStation.Railways[i].PlineID;
                closeLine[0].ds =ds;
                closeLine[0].footindex = tempfoot;
                min_ds = ds;
                min_i = curStation.Railways[i].PlineID;

                *tx_adjuest_dl = adx;
                *ty_adjuest_dl = ady;
                *min_ds_1 = min_ds;
                *cur_foot_index=tempfoot;
            }
            else if (ds <closeLine[1].ds && ds > closeLine[0].ds) {
                closeLine[2] = closeLine[1];
                closeLine[1].ds = ds;
                closeLine[1].lineid = curStation.Railways[i].PlineID;
                closeLine[1].footindex = tempfoot;
            }
            else if (ds <closeLine[2].ds && ds > closeLine[1].ds) {
                closeLine[2].ds = ds;
                closeLine[2].lineid = curStation.Railways[i].PlineID;
                closeLine[2].footindex = tempfoot;
            }
        }
  

    return min_i;
}

//检查股道的切换是否合理
//-1__不合理，跳出   1__保持现有股道不变   2__切换股道
int  CheckTheLineIDChange(DStation curStation, DSPoint curTrainPos,int lineid, int trainonlinedirect, int* cur_foot_index, float *tx_foot, float *ty_foot,int *line_change_waittime_ifneeded)
{
    bool IDchange = false;
    int stat=1;
    int tempfoot=-1;
    float adx,ady;
    //如果当前股道是距离机车第二近的股道，且误差在范围内，股道不需要切换
    if (closeLine[1].lineid == lineid) {
        float zz = find_d_toMulPline(curStation, lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
        if (zz >= 0 && zz < LOCO_DISTEN) {
            *tx_foot = adx;
            *ty_foot = ady;
            *cur_foot_index = tempfoot;
            return -2;
        }
    }
    if (trainonlinedirect == 1 ) {//机车方向和股道方向一致
        float zz;
        switch(curStation.Railways[lineid].nextLineN){
           case 1://当前线路前方有一条线路路
                int tempfoot;
                zz = find_d_toMulPline(curStation, curStation.Railways[lineid].nextLineIDs[0],
                                       curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                if (zz < 0) { //计算机车距离该线路的距离，不合理，跳出回调重新计算
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待30包数据后还是不合理，跳出回调函数，重新定位
                            *line_change_waittime_ifneeded = 0;
                            return -1;
                     }
                     else {//等待
                            float zz = find_d_toMulPline(curStation,lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                            if (zz >= 0) {
                                *tx_foot = adx;
                                *ty_foot = ady;
                                *cur_foot_index = tempfoot;
                                return -2;
                            }
                            else {
                                *line_change_waittime_ifneeded = 0;
                                return -1;
                            }
                     }
                }
                *tx_foot = adx;
                *ty_foot = ady;
                *cur_foot_index = tempfoot;
                *line_change_waittime_ifneeded = 0;
                IDchange = true;
                stat = 1;
                break;
           case 2:
                for (int ii = 0; ii < curStation.Railways[lineid].nextLineN; ii++)//找出距离最短的线路
                {
                    int tempid;
                    tempid = curStation.Railways[lineid].nextLineIDs[ii];
                        if (tempid == closeLine[0].lineid) {
                        IDchange = true;
                        stat = 1;
                        break;
                    }
                }
                if (!IDchange) {
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待13包数据后还是不合理，跳出回调函数，重新定位
                        *line_change_waittime_ifneeded = 0;
                        return -1;
                    }
                    else {
                        return -2;
                    }
                }
                break;

           default:
                *line_change_waittime_ifneeded = 0;
                return -1;
                break;
        }
   }
   else if (trainonlinedirect == 2) { //机车方向和股道方向相反
        float zz;
        switch(curStation.Railways[lineid].preLineN){
          case 1://当前线路前方有一条线路路
                int tempfoot;
                zz = find_d_toMulPline(curStation, curStation.Railways[lineid].preLineIDs[0],
                                       curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                if (zz < 0) { //计算机车距离该线路的距离，不合理，跳出回调重新计算
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待30包数据后还是不合理，跳出回调函数，重新定位
                            *line_change_waittime_ifneeded = 0;
                            return -1;
                     }
                     else {//等待
                            float zz = find_d_toMulPline(curStation,lineid, curTrainPos.x, curTrainPos.y, &tempfoot,&adx,&ady);
                            if (zz >= 0) {
                                *tx_foot = adx;
                                *ty_foot = ady;
                                *cur_foot_index = tempfoot;
                                return -2;
                            }
                            else {
                                *line_change_waittime_ifneeded = 0;
                                return -1;
                            }
                     }
                }
                *tx_foot = adx;
                *ty_foot = ady;
                *cur_foot_index = tempfoot;
                *line_change_waittime_ifneeded = 0;
                IDchange = true;
                stat = 1;
                break;

          case 2://当前线路前方有两条线路
                for (int ii = 0; ii < curStation.Railways[lineid].preLineN; ii++)//找出距离最短的线路
                {
                    int tempid;
                    tempid = curStation.Railways[lineid].preLineIDs[ii];
                    if (tempid == closeLine[0].lineid) {
                        IDchange = true;
                        stat = 1;
                        break;
                    }
                }
                if (!IDchange) {
                    *line_change_waittime_ifneeded = *line_change_waittime_ifneeded + 1;
                    if (*line_change_waittime_ifneeded > 30) {//等待13包数据后还是不合理，跳出回调函数，重新定位
                        *line_change_waittime_ifneeded = 0;
                        return -1;
                    }
                    else {
                        return -2;
                    }
                }
                break;

          default:
                *line_change_waittime_ifneeded = 0;
                return -1;
                break;
        }

   }

    if (!IDchange) {
        return -3;
    }
    else {
        return stat;
    }
}



//根据前后股道的顺序确定机车在切换股道上的初始方向
int GetTrainDirectInitial(DStation curStation,int preLID, int LID)
{

    float x1, y1, x2, y2;
    float x3, y3, x4, y4;
    float dx, dy;
    float d1, d2;
    if (preLID < 0) preLID = LID;

    x1 = curStation.Railways[preLID].Points[0].x;
    y1 = curStation.Railways[preLID].Points[0].y;
    x2 = curStation.Railways[preLID].Points[curStation.Railways[preLID].totalPointsN - 1].x;
    y2 = curStation.Railways[preLID].Points[curStation.Railways[preLID].totalPointsN - 1].y;

    x3 = curStation.Railways[LID].Points[0].x;
    y3 = curStation.Railways[LID].Points[0].y;
    dx = x3 - x1;
    dy = y3 - y1;
    d1 = sqrt(dx * dx + dy * dy);
    dx = x3 - x2;
    dy = y3 - y2;
    d2 = sqrt(dx * dx + dy * dy);

    if (d2 < 0.5 || d1 < 0.5)
        return 1;

    x4 = curStation.Railways[LID].Points[curStation.Railways[LID].totalPointsN - 1].x;
    y4 = curStation.Railways[LID].Points[curStation.Railways[LID].totalPointsN - 1].y;

    dx = x4 - x1;
    dy = y4 - y1;
    d1 = sqrt(dx * dx + dy * dy);
    dx = x4 - x2;
    dy = y4 - y2;
    d2 = sqrt(dx * dx + dy * dy);

    if (d2 < 0.5 || d1 < 0.5)
        return 2;

}

//确定当前线路机车前方的信号灯
void FindLamponLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalLampN; i++){//1 信号灯
        USHORT ti =  stMap.Railways[PlineID].Lamps[i];
        if((trainonlinedirect==1) && (stMap.Lamps[ti].lamptype == trainonlinedirect)
                                  && (trainPer<stMap.Lamps[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=1;
                   tempPoint.PointID = stMap.Lamps[ti].lamp_index;
                   strcpy(tempPoint.PointName,stMap.Lamps[ti].lamp_name);
                   tempPoint.PointV = stMap.Lamps[ti].lampvalue;
                   tempPoint.PointDis=(stMap.Lamps[ti].linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();

        }else if((trainonlinedirect==2) && (stMap.Lamps[ti].lamptype == trainonlinedirect)
                                       && (trainPer>stMap.Lamps[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=1;
            tempPoint.PointID = stMap.Lamps[ti].lamp_index;
            strcpy(tempPoint.PointName,stMap.Lamps[ti].lamp_name);
            tempPoint.PointV = stMap.Lamps[ti].lampvalue;
            tempPoint.PointDis=(trainPer-stMap.Lamps[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }

}
//查前方进路上的信号灯
void FindLamponRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalLampN; i++){//1 信号灯
        USHORT ti =  stMap.Railways[PlineID].Lamps[i];
        if((trainonlinedirect==1) && (stMap.Lamps[ti].lamptype == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=1;
                   tempPoint.PointID = stMap.Lamps[ti].lamp_index;
                   strcpy(tempPoint.PointName,stMap.Lamps[ti].lamp_name);
                   tempPoint.PointV = stMap.Lamps[ti].lampvalue;
                   tempPoint.PointDis=(tempdis+stMap.Lamps[ti].linepos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();

        }else if((trainonlinedirect==2) && (stMap.Lamps[ti].lamptype == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=1;
            tempPoint.PointID = stMap.Lamps[ti].lamp_index;
            strcpy(tempPoint.PointName,stMap.Lamps[ti].lamp_name);
            tempPoint.PointV = stMap.Lamps[ti].lampvalue;
            tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength-stMap.Lamps[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }

}

//确定当前线路机车前方的接触网终点标
void FindPwndonLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalPowerEndN; i++){ //2 接触网终点标
        USHORT ti =  stMap.Railways[PlineID].Powerends[i];
        if((trainonlinedirect==1) && (stMap.Powerends[ti].PowerEnd_type == trainonlinedirect)
                                  && (trainPer<stMap.Powerends[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=2;
                   tempPoint.PointID = stMap.Powerends[ti].PowerEnd_index;
                   strcpy(tempPoint.PointName,"终点标");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.Powerends[ti].linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();

        }else if((trainonlinedirect==2) && (stMap.Powerends[ti].PowerEnd_type == trainonlinedirect)
                                       && (trainPer>stMap.Powerends[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=2;
            tempPoint.PointID = stMap.Powerends[ti].PowerEnd_index;
            strcpy(tempPoint.PointName,"终点标");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.Powerends[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }
}
//查前方进路上的接触网终点标
void FindPwndonRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalPowerEndN; i++){ //2 接触网终点标
        USHORT ti =  stMap.Railways[PlineID].Powerends[i];
        if((trainonlinedirect==1) && (stMap.Powerends[ti].PowerEnd_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=2;
                   tempPoint.PointID = stMap.Powerends[ti].PowerEnd_index;
                   strcpy(tempPoint.PointName,"终点标");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap.Powerends[ti].linepos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();

        }else if((trainonlinedirect==2) && (stMap.Powerends[ti].PowerEnd_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=2;
            tempPoint.PointID = stMap.Powerends[ti].PowerEnd_index;
            strcpy(tempPoint.PointName,"终点标");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+ stMap.Railways[PlineID].LineLength-stMap.Powerends[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }
}


//确定当前线路机车前方的脱轨器
void FindDeraonLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalDerailerN; i++){ //3 脱轨器
        USHORT ti =  stMap.Railways[PlineID].Derailers[i];
        if((trainonlinedirect==1) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)
                                  && (trainPer<stMap.Derailers[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=3;
                   tempPoint.PointID = stMap.Derailers[ti].Derailer_index;
                   strcpy(tempPoint.PointName,"脱轨器");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.Derailers[ti].linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)
                                       && (trainPer>stMap.Derailers[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=3;
            tempPoint.PointID = stMap.Derailers[ti].Derailer_index;
            strcpy(tempPoint.PointName,"脱轨器");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.Derailers[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}
//查前方进路上的脱轨器
void FindDeraonRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalDerailerN; i++){ //3 脱轨器
        USHORT ti =  stMap.Railways[PlineID].Derailers[i];
        if((trainonlinedirect==1) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=3;
                   tempPoint.PointID = stMap.Derailers[ti].Derailer_index;
                   strcpy(tempPoint.PointName,"脱轨器");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap.Derailers[ti].linepos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=3;
            tempPoint.PointID = stMap.Derailers[ti].Derailer_index;
            strcpy(tempPoint.PointName,"脱轨器");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength-stMap.Derailers[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}
//确定当前线路机车前方的站界
void FindStlionLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalStLimitN; i++){ //5 站界
        USHORT ti =  stMap.Railways[PlineID].StLimits[i];
        if((trainonlinedirect==1) && (stMap.StLimits[ti].StLimit_type == trainonlinedirect)
                                  && (trainPer<stMap.StLimits[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=5;
                   tempPoint.PointID = stMap.StLimits[ti].StLimit_index;
                   strcpy(tempPoint.PointName,"站界");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.StLimits[ti].linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();

        }else if((trainonlinedirect==2) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)
                                       && (trainPer>stMap.Derailers[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=5;
            tempPoint.PointID = stMap.StLimits[ti].StLimit_index;
            strcpy(tempPoint.PointName,"站界");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.StLimits[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}
//查前方进路上的脱轨器站界
void FindStlionRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalStLimitN; i++){ //5 站界
        USHORT ti =  stMap.Railways[PlineID].StLimits[i];
        if((trainonlinedirect==1) && (stMap.StLimits[ti].StLimit_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=5;
                   tempPoint.PointID = stMap.StLimits[ti].StLimit_index;
                   strcpy(tempPoint.PointName,"站界");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap.StLimits[ti].linepos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.Derailers[ti].Derailer_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=5;
            tempPoint.PointID = stMap.StLimits[ti].StLimit_index;
            strcpy(tempPoint.PointName,"站界");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength-stMap.StLimits[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}



//确定当前线路机车前方的土挡
void FindStoponLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalStoppointN; i++){ //4 土挡
        USHORT ti =  stMap.Railways[PlineID].Stoppoints[i];
        if((trainonlinedirect==1) && (stMap.Stoppoints[ti].Stype == trainonlinedirect)
                                  && (trainPer<stMap.Stoppoints[ti].line_pos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=4;
                   tempPoint.PointID = stMap.Stoppoints[ti].s_id;
                   strcpy(tempPoint.PointName,stMap.Stoppoints[ti].Stoppoint_name);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.Stoppoints[ti].line_pos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.Stoppoints[ti].Stype == trainonlinedirect)
                                       && (trainPer>stMap.Stoppoints[ti].line_pos)){
            FKPoint tempPoint;
            tempPoint.PointType=4;
            tempPoint.PointID = stMap.Stoppoints[ti].s_id;
            strcpy(tempPoint.PointName,stMap.Stoppoints[ti].Stoppoint_name);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.Stoppoints[ti].line_pos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}
//查前方进路上的土挡
void FindStoponRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalStoppointN; i++){ //4 土挡
        USHORT ti =  stMap.Railways[PlineID].Stoppoints[i];
        if((trainonlinedirect==1) && (stMap.Stoppoints[ti].Stype == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=4;
                   tempPoint.PointID = stMap.Stoppoints[ti].s_id;
                   strcpy(tempPoint.PointName,stMap.Stoppoints[ti].Stoppoint_name);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis+stMap.Stoppoints[ti].line_pos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.Stoppoints[ti].Stype == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=4;
            tempPoint.PointID = stMap.Stoppoints[ti].s_id;
            strcpy(tempPoint.PointName,stMap.Stoppoints[ti].Stoppoint_name);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength-stMap.Stoppoints[ti].line_pos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}

//确定当前线路机车前方的无网线路
void  FindWwquonLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    if (trainonlinedirect == 1) {
        for (int i = 0; i < stMap.Railways[PlineID].nextLineN; i++) {
            USHORT ti= stMap.Railways[PlineID].nextLineIDs[i];
            if (stMap.Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(stMap.Railways[PlineID].LineLength-trainPer)*100;
                Global_Var.mutexKP.lock();
                Global_Var.KeyPointS.push_back(tempPoint);
                Global_Var.mutexKP.unlock();
            }
        }
    }
    else if (trainonlinedirect == 2) {
        for (int i = 0; i < stMap.Railways[PlineID].preLineN; i++) {
            USHORT ti= stMap.Railways[PlineID].preLineIDs[i];
            if (stMap.Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=trainPer*100;
                Global_Var.mutexKP.lock();
                Global_Var.KeyPointS.push_back(tempPoint);
                Global_Var.mutexKP.unlock();
            }
        }
    }
}
//查前方进路上的无网线路
void  FindWwquonRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    if (trainonlinedirect == 1) {
        for (int i = 0; i < stMap.Railways[PlineID].nextLineN; i++) {
            USHORT ti= stMap.Railways[PlineID].nextLineIDs[i];
            if (stMap.Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength)*100;
                Global_Var.mutexKP.lock();
                Global_Var.KeyPointS.push_back(tempPoint);
                Global_Var.mutexKP.unlock();
            }
        }
    }
    else if (trainonlinedirect == 2) {
        for (int i = 0; i < stMap.Railways[PlineID].preLineN; i++) {
            USHORT ti= stMap.Railways[PlineID].preLineIDs[i];
            if (stMap.Railways[ti].NonPowerStat == 1) {
                FKPoint tempPoint;
                tempPoint.PointType=7;
                tempPoint.PointID = ti;
                strcpy(tempPoint.PointName,"无网区");
                tempPoint.PointV = 0;
                tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength)*100;
                Global_Var.mutexKP.lock();
                Global_Var.KeyPointS.push_back(tempPoint);
                Global_Var.mutexKP.unlock();
            }
        }
    }
}



//确定当前线路机车前方的特殊防控项点
void FindSpeconLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalSPN; i++){ //8 特殊防控项点
        USHORT ti =  stMap.Railways[PlineID].SpecPoints[i];
        if((trainonlinedirect==1) && (stMap.SpecPoints[ti].sp_type == trainonlinedirect)
                                  && (trainPer<stMap.SpecPoints[ti].linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=8;
                   tempPoint.PointID = stMap.SpecPoints[ti].sp_index;
                   strcpy(tempPoint.PointName,stMap.SpecPoints[ti].SPName);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.SpecPoints[ti].linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.SpecPoints[ti].sp_type == trainonlinedirect)
                                       && (trainPer>stMap.SpecPoints[ti].linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=8;
            tempPoint.PointID = stMap.SpecPoints[ti].sp_index;
            strcpy(tempPoint.PointName,stMap.SpecPoints[ti].SPName);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.SpecPoints[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}
//查前方进路上的特殊防控项点
void FindSpeconRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalSPN; i++){ //8 特殊防控项点
        USHORT ti =  stMap.Railways[PlineID].SpecPoints[i];
        if((trainonlinedirect==1) && (stMap.SpecPoints[ti].sp_type == trainonlinedirect)){
                   FKPoint tempPoint;
                   tempPoint.PointType=8;
                   tempPoint.PointID = stMap.SpecPoints[ti].sp_index;
                   strcpy(tempPoint.PointName,stMap.SpecPoints[ti].SPName);
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(tempdis + stMap.SpecPoints[ti].linepos)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2) && (stMap.SpecPoints[ti].sp_type == trainonlinedirect)){
            FKPoint tempPoint;
            tempPoint.PointType=8;
            tempPoint.PointID = stMap.SpecPoints[ti].sp_index;
            strcpy(tempPoint.PointName,stMap.SpecPoints[ti].SPName);
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis+stMap.Railways[PlineID].LineLength-stMap.SpecPoints[ti].linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
       }
    }
}



//确定当前线路机车前方的禁停区
void FindNPAonLine(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalNPAN; i++){ //9 禁停区
        USHORT ti =  stMap.Railways[PlineID].NPAs[i];
        if((trainonlinedirect==1)  && (trainPer<stMap.NPAs[ti].begin_linepos) ){
                   FKPoint tempPoint;
                   tempPoint.PointType=9;
                   tempPoint.PointID = stMap.NPAs[ti].NPA_index;
                   strcpy(tempPoint.PointName,"禁停区");
                   tempPoint.PointV = 0;
                   tempPoint.PointDis=(stMap.NPAs[ti].begin_linepos-trainPer)*100;
                   Global_Var.mutexKP.lock();
                   Global_Var.KeyPointS.push_back(tempPoint);
                   Global_Var.mutexKP.unlock();
        }else if((trainonlinedirect==2)  && (trainPer>stMap.NPAs[ti].end_linepos)){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap.NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(trainPer-stMap.NPAs[ti].end_linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }else if(trainPer<=stMap.NPAs[ti].end_linepos && trainPer>=stMap.NPAs[ti].begin_linepos){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap.NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=0;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }

}

//查前方进路上的禁停区
void FindNPAonRail(DStation stMap, float tempdis, int PlineID, int trainonlinedirect)
{
    for(int i =0; i<stMap.Railways[PlineID].totalNPAN; i++){ //9 禁停区
        USHORT ti =  stMap.Railways[PlineID].NPAs[i];
        if(trainonlinedirect==1){
           FKPoint tempPoint;
           tempPoint.PointType=9;
           tempPoint.PointID = stMap.NPAs[ti].NPA_index;
           strcpy(tempPoint.PointName,"禁停区");
           tempPoint.PointV = 0;
           tempPoint.PointDis=(tempdis+stMap.NPAs[ti].begin_linepos)*100;
           Global_Var.mutexKP.lock();
           Global_Var.KeyPointS.push_back(tempPoint);
           Global_Var.mutexKP.unlock();
        }else if(trainonlinedirect==2){
            FKPoint tempPoint;
            tempPoint.PointType=9;
            tempPoint.PointID = stMap.NPAs[ti].NPA_index;
            strcpy(tempPoint.PointName,"禁停区");
            tempPoint.PointV = 0;
            tempPoint.PointDis=(tempdis + stMap.Railways[PlineID].LineLength-stMap.NPAs[ti].end_linepos)*100;
            Global_Var.mutexKP.lock();
            Global_Var.KeyPointS.push_back(tempPoint);
            Global_Var.mutexKP.unlock();
        }
    }

}

//确定机车上下行状态
char Get_Line_xb(DStation stMap, int PlineID, int trainonlinedirect)
{
   char stat=2;
   if(trainonlinedirect==1){
       if(stMap.Railways[PlineID].up_type==1)//上行
                  stat=0;
       else if(stMap.Railways[PlineID].up_type==2)//下行
                  stat=1;
   }else if(trainonlinedirect==2){
       if(stMap.Railways[PlineID].up_type==1)//上行
                  stat=1;
       else if(stMap.Railways[PlineID].up_type==2)//下行
                  stat=0;

   }
   return stat;
}

//确定机车上下行状态
uint32_t Get_DisToDC(DStation stMap, int PlineID, float per,int trainonlinedirect)
{
   uint32_t stat=2;
   if(trainonlinedirect==1){
        stat=(stMap.Railways[PlineID].LineLength-per)*100;
   }else if(trainonlinedirect==2){
        stat=per*100;
   }
   return stat;
}

//确定机车前方进路和防控项
//确定机车前方进路和防控项点,存入全局变量中的进路容器和防控项点容器
void FindtheWayandPoint(DStation stMap, float trainPer, int PlineID, int trainonlinedirect)
{
   if(trainonlinedirect==0) return;
   Global_Var.mutexKP.lock();
   Global_Var.KeyPointS.clear();
   Global_Var.mutexKP.unlock();

   Global_Var.mutexKL.lock();
   Global_Var.KeyLineS.clear();
   Global_Var.mutexKL.unlock();

   //当前线路压入容器
   Railway tempway;
   tempway.RailwayID = PlineID;
   tempway.RailLength = stMap.Railways[PlineID].LineLength*100;
   tempway.xb =Get_Line_xb(stMap,PlineID,trainonlinedirect);
   tempway.DisToDC = Get_DisToDC(stMap, PlineID, trainPer,trainonlinedirect);
   tempway.MaxSpeed =curTaxData.MaxSpeed;
   tempway.direct = trainonlinedirect;
   strcpy(tempway.RailwayName,stMap.Railways[PlineID].line_name);
   Global_Var.mutexKL.lock();
   Global_Var.KeyLineS.push_back(tempway);
   Global_Var.mutexKL.unlock();

   //查找当前线路的防控项点
   FindLamponLine(stMap, trainPer, PlineID, trainonlinedirect);//1 信号灯
   FindPwndonLine(stMap, trainPer, PlineID, trainonlinedirect);//2 接触网终点标
   FindDeraonLine(stMap, trainPer, PlineID, trainonlinedirect);//3 脱轨器
   FindStlionLine(stMap, trainPer, PlineID, trainonlinedirect);//5 站界
   FindStoponLine(stMap, trainPer, PlineID, trainonlinedirect);//4 土挡
   FindWwquonLine(stMap, trainPer, PlineID, trainonlinedirect);//7 无网线路
   FindSpeconLine(stMap, trainPer, PlineID, trainonlinedirect);//8 特殊防控项点
   FindNPAonLine(stMap,  trainPer, PlineID, trainonlinedirect);//9 禁停区


   //检查本线路外是否有防控项点
   int nextID = -1;
   float ptx, pty;
   float tempdis = 0;
   float  temp_train_head = 0;
   if ((stMap.Railways[PlineID].nextLineN == 1) && (trainonlinedirect == 1)) {  // 正向
       ptx = stMap.Railways[PlineID].Points[stMap.Railways[PlineID].totalPointsN - 1].x;
       pty = stMap.Railways[PlineID].Points[stMap.Railways[PlineID].totalPointsN - 1].y;
       nextID = stMap.Railways[PlineID].nextLineIDs[0];
       tempdis=stMap.Railways[PlineID].LineLength-trainPer;
   }
   else if ((stMap.Railways[PlineID].preLineN == 1) && (trainonlinedirect == 2)) {// 反向
       ptx = stMap.Railways[PlineID].Points[0].x;
       pty = stMap.Railways[PlineID].Points[0].y;
       nextID = stMap.Railways[PlineID].preLineIDs[0];
       tempdis= trainPer;
   }

   if (nextID == -1) {//前方无进路
       return ;
   }

   while (1) {//检查前方进路上的防控项点
      int pn = stMap.Railways[nextID].totalPointsN - 1;
      float x_0 = stMap.Railways[nextID].Points[0].x;
      float y_0 = stMap.Railways[nextID].Points[0].y;
      float x_n = stMap.Railways[nextID].Points[pn].x;
      float y_n = stMap.Railways[nextID].Points[pn].y;
      float d_0 = (x_0 - ptx) * (x_0 - ptx) + (y_0 - pty) * (y_0 - pty);
      float d_n = (x_n - ptx) * (x_n - ptx) + (y_n - pty) * (y_n - pty);
      if (d_0 < d_n)
          temp_train_head = 1;
      else if (d_0 > d_n)
          temp_train_head = 2;

      //进路压入容器
      Railway nextline;
      nextline.RailwayID = nextID;
      nextline.RailLength = stMap.Railways[nextID].LineLength*100;
      nextline.xb =Get_Line_xb(stMap,nextID,temp_train_head);
      nextline.DisToDC = nextline.RailLength;
      nextline.MaxSpeed =1999;
      nextline.direct = temp_train_head;
      Global_Var.mutexKL.lock();
      Global_Var.KeyLineS.push_back(nextline);
      Global_Var.mutexKL.unlock();

      FindLamponRail(stMap, tempdis, nextID, temp_train_head);//1 信号灯
      FindPwndonRail(stMap, tempdis, nextID, temp_train_head);//2 接触网终点标
      FindDeraonRail(stMap, tempdis, nextID, temp_train_head);//3 脱轨器
      FindStlionRail(stMap, tempdis, nextID, temp_train_head);//5 站界
      FindStoponRail(stMap, tempdis, nextID, temp_train_head);//4 土挡
      FindWwquonRail(stMap, tempdis, nextID, temp_train_head);//7 无网线路
      FindSpeconRail(stMap, tempdis, nextID, temp_train_head);//8 特殊防控项点
      FindNPAonRail(stMap,  tempdis, nextID, temp_train_head);//9 禁停区

      if (temp_train_head == 1) {//如果正向

          if (stMap.Railways[nextID].nextLineN == 1) {
              tempdis = tempdis +stMap.Railways[nextID].LineLength;
              ptx = stMap.Railways[nextID].Points[stMap.Railways[nextID].totalPointsN - 1].x;
              pty = stMap.Railways[nextID].Points[stMap.Railways[nextID].totalPointsN - 1].y;
              nextID = stMap.Railways[nextID].nextLineIDs[0];
          }
          else {
              break;
          }
      }
      else if (temp_train_head == 2) {//如果反向
          if (stMap.Railways[nextID].preLineN == 1) {
              tempdis = tempdis + stMap.Railways[nextID].LineLength;
              ptx = stMap.Railways[nextID].Points[0].x;
              pty = stMap.Railways[nextID].Points[0].y;
              nextID = stMap.Railways[nextID].preLineIDs[0];
          }
          else {
              break;
          }
      }
   }
   //进路上的防控项点按距离近远排序
   sort(Global_Var.KeyPointS.begin(), Global_Var.KeyPointS.end(), [](const FKPoint& a, const FKPoint& b) {
       return a.PointDis < b.PointDis;
     });
   Global_Var.KeyPoint_N=Global_Var.KeyPointS.size();
   Global_Var.KeyLine_N = Global_Var.KeyLineS.size();
}

//计算机车距轨道起点的距离，单位米
float find_disp_to_begin(DStation stMap, DSPoint trainPos, int PlineID, int trainindex)
{
    int last_i = -1;
    float dis = 0;
    float lx, ly, dx, dy;
    float txx,tyy;
    txx=trainPos.x;
    tyy=trainPos.y;

    if (PlineID < 0) {
        return -1;
    }

    lx = stMap.Railways[PlineID].Points[0].x;
    ly = stMap.Railways[PlineID].Points[0].y;

    last_i = trainindex;

    if (last_i == 0) {
        dx = txx - lx;
        dy = tyy - ly;
        dis = sqrt(dx * dx + dy * dy);
    }
    else if(last_i >0){
        dis = 0;
        for (int i = 0;i < last_i;i++) {
            float x1 = stMap.Railways[PlineID].Points[i].x;
            float y1 = stMap.Railways[PlineID].Points[i].y;
            float x2 = stMap.Railways[PlineID].Points[i + 1].x;
            float y2 = stMap.Railways[PlineID].Points[i + 1].y;
            dx = x1 - x2;
            dy = y1 - y2;
            dis=dis+ sqrt(dx * dx + dy * dy);
        }
        dx = txx - stMap.Railways[PlineID].Points[last_i].x;
        dy = tyy - stMap.Railways[PlineID].Points[last_i].y;
        dis = dis + sqrt(dx * dx + dy * dy);
    }
    else {
        dis = -1;
    }
    if (dis == 0) {
        dis = 0;
    }

    return dis;
}

void * Thread_Chesuan(void *lpPara)
{

   bool showmap_stat=true;      //控制地图是否显示
   DStation curStation;         //当前站场地图
   DSPoint  curTrainPos;        //当前北斗天线投影坐标
   DSPoint  trainFoot,trainSP;
   int  cur_foot_index=-1;             //当前gps天线所在区间
   int  curLineID =-1, preLineID=-1;   //当前,上一次股道编号
   int trainonlinedirect=0,pre_trainonlinedirect;    //机车运行方向
                                                     //  1 : 从线路起点向终点运行
                                                     //  2 : 从线路终点向起点运行
   int inlinedataid = 0;
   int line_change_waittime_ifneeded=0;

#if TESTStat
       strcpy(TrainInfo.EngineTypeStr,"HXD1C");
       TrainInfo.EngineNo=345;
       int  socket_send;
       socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
       struct sockaddr_in ClientAddrTest;
       memset(&ClientAddrTest, 0, sizeof(ClientAddrTest));
       ClientAddrTest.sin_family = AF_INET;
       ClientAddrTest.sin_port = htons(10001);
       ClientAddrTest.sin_addr.s_addr = inet_addr("192.168.60.71");
#endif

   while(1)
   {
        if (!GPSState || GPSData.State == 0) continue;
        //GPS数据不正常
        if (!(GPSData.latitude > 3 && GPSData.latitude < 54 && GPSData.longitude >73 && GPSData.longitude < 136))
        {
           //GPS数据不正常时用速度时间计算距离
           //rk_syslog("GPS数据超出中国范围");
           continue;
        }
        Global_Var.preStationIndex = Global_Var.curStationIndex;
#if TESTStat
        Global_Var.curStationIndex=7;
        TrainInfo.StationID = Global_Var.curStationIndex;
#else
        Global_Var.curStationIndex = CheckifinStation(GPSData.longitude,GPSData.latitude);//确定机车是否在站场内
#endif
        if(Global_Var.curStationIndex==-1 && Global_Var.preStationIndex==-1) continue;
        if(Global_Var.curStationIndex==-1 && Global_Var.preStationIndex!=-1){ //离开站场
            char tempbuf[128];
            sprintf(tempbuf,"离开站场[%s]",Global_Var.stationName);
            rk_syslog(tempbuf);
            continue;
        }
        if(Global_Var.curStationIndex!=-1 && Global_Var.preStationIndex==-1){ //进入站场
            if(!LoadStation_Map(Global_Var.curStationIndex,&curStation)){//加载当前站场地图
                char tempbuf[128];
                sprintf(tempbuf,"读取地图文件[%s]失败",GetMapFileName(Global_Var.curStationIndex));
                rk_syslog(tempbuf);
                continue;
            }
            char tempbuf[128];
            sprintf(tempbuf,"进入站场[%s]",Global_Var.stationName);
            rk_syslog(tempbuf);
        }

#if TESTStat
        PCRDCARTESIAN pcc;
        pcc.y = tx_test;
        pcc.x = ty_test;
#else
        PCRDCARTESIAN pcc;
        PCRDGEODETIC pcg;
        pcg.longitude = GPSData.longitude;
        pcg.latitude = GPSData.latitude;
        pcg.height = GPSData.height;
        GeodeticToCartesian(pcc,pcg);//经纬度转投影坐标
#endif

        curTrainPos.x = pcc.y;
        curTrainPos.y = pcc.x;
        PushBDSQueue(curTrainPos); //把最新的连续10包BDS坐标数据存入队列
        if(BDS_Que.size()<6) continue;
        //开始定位火车位置
        float min_ds;
        int min_i;
        min_i=GetTheClosestLineToTheTrain(curStation,curTrainPos,&min_ds, &cur_foot_index,
                                         &trainFoot.x, &trainFoot.y);
        //运行前方仅有1条股道，放大控制误差
        float tempds;
        tempds = LOCO_DISTEN;
        if(curLineID >= 0 ){
            if ((curStation.Railways[curLineID].nextLineN == 1 && trainonlinedirect == 1 )
                || (curStation.Railways[curLineID].preLineN == 1 && trainonlinedirect == 2)) {
                tempds = 3 * LOCO_DISTEN;
            }
        }
        if (min_ds >tempds){
            curLineID = -1;
            continue;
        }

        if(cur_foot_index < 0)  continue;

        //如果定位出的股道编号和当前股道编号不一致，检查股道的切换是否合理
        if (curLineID != curStation.Railways[min_i].PlineID && curLineID !=-1){
            int checkResult=CheckTheLineIDChange(curStation, curTrainPos,curLineID, trainonlinedirect,
                                 &cur_foot_index, &trainFoot.x, &trainFoot.y,&line_change_waittime_ifneeded);
            if (checkResult == -1) {//不合理，跳出计算
                preLineID = -1;
                curLineID = -1;
                trainonlinedirect = 0;
                SetTrainDirect(0);
                continue;
            }
            else if (checkResult == -2) {//等待
                preLineID = curLineID;
            }
            else if (checkResult == 1) {//切换股道，初始化相关状态参数
                preLineID = curLineID;
                curLineID = curStation.Railways[min_i].PlineID;
                //切换线路时，初始化相关状态参数
                showmap_stat = true;
                pre_trainonlinedirect = trainonlinedirect;
                trainonlinedirect = GetTrainDirectInitial(curStation, preLineID,curLineID);//根据前后股道的顺序确定机车在切换股道上的初始方向
                SetTrainDirect(trainonlinedirect);
                inlinedataid = 0;
                SetLinePer(0);
                //前方有两股道时，开始计算距离，20米以后才显示；
                if(showmap_stat && ((curStation.Railways[preLineID].nextLineN> 1 && trainonlinedirect == 1) ||
                                    (curStation.Railways[preLineID].preLineN > 1 && trainonlinedirect == 2)))
                {
                    showmap_stat = false;
                    trainSP=curTrainPos;
                }
            }
        }
        else {//如果定位出的股道编号和当前股道编号一致，继续在轨计算
            preLineID = curLineID;
            curLineID = curStation.Railways[min_i].PlineID;
        }

        //前方有两岔道时，开始计算距离，20米以后才显示；
        if (!showmap_stat && (GetDS(curTrainPos,trainSP) > 25)) {
             showmap_stat = true;
        }

        //以下计算机车BDS坐标用原坐标在轨道中心线的垂足坐标代替
        //计算机车距轨道起点的距离，单位米
        float per = find_disp_to_begin(curStation,trainFoot, curLineID, cur_foot_index);
        //此处要修正per，缺函数
        //??????????????????

        PutLinePer(per);
        if(inlinedataid<6) inlinedataid = inlinedataid + 1;
        if(inlinedataid>5){ //确定机车在轨道上的运行方向
            Check_Per_Direct();
            if (check_if_ok()) {//机车运行切换方向 必须连续5次判断一致
                pre_trainonlinedirect = trainonlinedirect;
                trainonlinedirect = train_line_direct[0];
            }
        }
        //确定机车前方进路和防控项点
        FindtheWayandPoint(curStation,per, curLineID,trainonlinedirect);

#if TESTStat
        TrainInfo.direction=trainonlinedirect;
        TrainInfo.LinePer = per / curStation.Railways[curLineID].LineLength;
        strcpy(TrainInfo.LineName,curStation.Railways[curLineID].line_name);
        if(Global_Var.KeyPointS.size()>0){
            TrainInfo.PointType = Global_Var.KeyPointS[0].PointType;
            TrainInfo.DistoP    = Global_Var.KeyPointS[0].PointDis/100.0;
            strcpy(TrainInfo.LampName,Global_Var.KeyPointS[0].PointName);
        }else if(Global_Var.KeyPointS.size()==0){
            TrainInfo.PointType=6;
            if(trainonlinedirect==1)
                TrainInfo.DistoP=curStation.Railways[curLineID].LineLength-per;
            else if(trainonlinedirect==2)
                TrainInfo.DistoP = per;
        }
        sendto(socket_send,(char*)&TrainInfo,sizeof(TrainInfo),0,(struct sockaddr *)&ClientAddrTest,sizeof(ClientAddrTest));

#endif
        usleep(200000);//200ms 5Hz
  }
	
}

//车算测试用模拟数据接收线程
void *Test_Data_For_Chesuan(void *lpPara)
{

    bool showmap_stat=true;      //控制地图是否显示
    DStation curStation;         //当前站场地图
    DSPoint  curTrainPos;        //当前北斗天线投影坐标
    DSPoint  trainFoot,trainSP;
    int  cur_foot_index=-1;             //当前gps天线所在区间
    int  curLineID =-1, preLineID=-1;   //当前,上一次股道编号
    int trainonlinedirect=0,pre_trainonlinedirect;    //机车运行方向
                                                      //  1 : 从线路起点向终点运行
                                                      //  2 : 从线路终点向起点运行
    int inlinedataid = 0;
    int line_change_waittime_ifneeded=0;

 #if TESTStat
        strcpy(TrainInfo.EngineTypeStr,"HXD1C");
        TrainInfo.EngineNo=345;
        int  socket_send;
        socket_send=socket(AF_INET,SOCK_DGRAM,0); //USE UDP
        struct sockaddr_in ClientAddrTest;
        memset(&ClientAddrTest, 0, sizeof(ClientAddrTest));
        ClientAddrTest.sin_family = AF_INET;
        ClientAddrTest.sin_port = htons(10001);
        ClientAddrTest.sin_addr.s_addr = inet_addr("192.168.60.71");
 #endif

  byte buf[1024] = { 0 };
  int SocToGround = 0;
  SocToGround = socket(AF_INET, SOCK_DGRAM, 0);
  struct sockaddr_in LocAddr;
  memset(&LocAddr, 0, sizeof(LocAddr));
  LocAddr.sin_family = AF_INET;
  LocAddr.sin_port = htons(10001);
  LocAddr.sin_addr.s_addr = inet_addr("192.168.60.73");
  //LocAddr.sin_addr.s_addr = INADDR_ANY;
  if(bind(SocToGround, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0)
      return ((void*)0);
  float m_longitude = 0, m_latitude = 0;
  while(1){
    memset(buf,0,1024);
    int res = recv(SocToGround,buf,sizeof(buf),0);
    if(res<=0) continue;
    unsigned char sum = 0;
    sum=AT1_checksc(res-1,buf);
    if(sum != buf[res-1])  continue;
    if ((buf[0] == 0xFF) && (buf[1] == 0x01) )  //如果接收到模拟BDS数据
    {
       tx_test=*(double *)&buf[25];
       ty_test=*(double *)&buf[17];
       m_longitude = *(float *)&buf[52];
       m_latitude  = *(float *)&buf[56];
       GPSData.latitude = m_latitude;
       GPSData.longitude = m_longitude;
       GPSState=true;
       GPSData.State=buf[49];
       TrainInfo.TAXTime.Year = buf[43] + 2000;
       TrainInfo.TAXTime.Month = buf[44];
       TrainInfo.TAXTime.Day = buf[45];
       TrainInfo.TAXTime.Hour = buf[46];
       TrainInfo.TAXTime.Minute = buf[47];
       TrainInfo.TAXTime.Second = buf[48];
       TrainInfo.TAXStat = TaxStat;
       TrainInfo.GPSStat = buf[49];
       TrainInfo.Speed = buf[41] + 256 * buf[42];

       Global_Var.BDS_Stat= GPSData.State;
       Global_Var.Speed = TrainInfo.Speed;
       Global_Var.TAX_Stat = 1;

       if (!GPSState || GPSData.State == 0) continue;
       //GPS数据不正常
       if (!(GPSData.latitude > 3 && GPSData.latitude < 54 && GPSData.longitude >73 && GPSData.longitude < 136))
       {
          //GPS数据不正常时用速度时间计算距离
          //rk_syslog("GPS数据超出中国范围");
          continue;
       }
       Global_Var.preStationIndex = Global_Var.curStationIndex;
#if TESTStat
       Global_Var.curStationIndex=7;
       TrainInfo.StationID = Global_Var.curStationIndex;
#else
       Global_Var.curStationIndex = CheckifinStation(GPSData.longitude,GPSData.latitude);//确定机车是否在站场内
#endif
       if(Global_Var.curStationIndex==-1 && Global_Var.preStationIndex==-1) continue;
       if(Global_Var.curStationIndex==-1 && Global_Var.preStationIndex!=-1){ //离开站场
           char tempbuf[128];
           sprintf(tempbuf,"离开站场[%s]",Global_Var.stationName);
           rk_syslog(tempbuf);
           continue;
       }
       if(Global_Var.curStationIndex!=-1 && Global_Var.preStationIndex==-1){ //进入站场
           if(!LoadStation_Map(Global_Var.curStationIndex,&curStation)){//加载当前站场地图
               char tempbuf[128];
               sprintf(tempbuf,"读取地图文件[%s]失败",GetMapFileName(Global_Var.curStationIndex));
               rk_syslog(tempbuf);
               continue;
           }
           char tempbuf[128];
           sprintf(tempbuf,"进入站场[%s]",Global_Var.stationName);
           rk_syslog(tempbuf);
       }

#if TESTStat
       PCRDCARTESIAN pcc;
       pcc.y = tx_test;
       pcc.x = ty_test;
#else
       PCRDCARTESIAN pcc;
       PCRDGEODETIC pcg;
       pcg.longitude = GPSData.longitude;
       pcg.latitude = GPSData.latitude;
       pcg.height = GPSData.height;
       GeodeticToCartesian(pcc,pcg);//经纬度转投影坐标
#endif
       long t0= what_time_is_it_now();
       curTrainPos.x = pcc.y;
       curTrainPos.y = pcc.x;
       PushBDSQueue(curTrainPos); //把最新的连续10包BDS坐标数据存入队列
       if(BDS_Que.size()<6) continue;
       //开始定位火车位置
       float min_ds;
       int min_i;

       //运行前方仅有1条股道，放大控制误差
       float tempds;
       tempds = LOCO_DISTEN;
       if(curLineID >= 0 ){
           min_ds = find_d_toMulPline(curStation, curStation.Railways[curLineID].PlineID,
                                      curTrainPos.x, curTrainPos.y,
                                      &cur_foot_index, &trainFoot.x, &trainFoot.y);

           if ((curStation.Railways[curLineID].nextLineN == 1 && trainonlinedirect == 1 )
               || (curStation.Railways[curLineID].preLineN == 1 && trainonlinedirect == 2)) {
               tempds = 3 * LOCO_DISTEN;
           }
           if(min_ds >tempds || min_ds<0) {
               curLineID=-1;
               continue;
           }
           min_i= curLineID;

       }else{

           min_i=GetTheClosestLineToTheTrain(curStation,curTrainPos,&min_ds, &cur_foot_index,
                                               &trainFoot.x, &trainFoot.y);
           if(min_ds >tempds || min_i==-1) {
               curLineID=-1;
               continue;
           }
       }

       if(cur_foot_index < 0)  continue;


       //long t1=what_time_is_it_now();
       //printf("t1 = %d\n",t1-t0);
       //如果定位出的股道编号和当前股道编号不一致，检查股道的切换是否合理
       if (curLineID != curStation.Railways[min_i].PlineID && curLineID !=-1){
           int checkResult=CheckTheLineIDChange(curStation, curTrainPos,curLineID, trainonlinedirect,
                                &cur_foot_index, &trainFoot.x, &trainFoot.y,&line_change_waittime_ifneeded);
           if (checkResult == -1) {//不合理，跳出计算
               preLineID = -1;
               curLineID = -1;
               trainonlinedirect = 0;
               SetTrainDirect(0);
               continue;
           }
           else if (checkResult == -2) {//等待
               preLineID = curLineID;
               continue;
           }
           else if (checkResult == 1) {//切换股道，初始化相关状态参数
               preLineID = curLineID;
               curLineID = curStation.Railways[min_i].PlineID;
               //切换线路时，初始化相关状态参数
               showmap_stat = true;
               pre_trainonlinedirect = trainonlinedirect;
               trainonlinedirect = GetTrainDirectInitial(curStation, preLineID,curLineID);//根据前后股道的顺序确定机车在切换股道上的初始方向
               SetTrainDirect(trainonlinedirect);
               inlinedataid = 0;
               SetLinePer(0);
               //前方有两股道时，开始计算距离，20米以后才显示；
               if(showmap_stat && ((curStation.Railways[preLineID].nextLineN> 1 && trainonlinedirect == 1) ||
                                   (curStation.Railways[preLineID].preLineN > 1 && trainonlinedirect == 2)))
               {
                   showmap_stat = false;
                   trainSP=curTrainPos;
               }
           }
           if (cur_foot_index < 0)  continue;
       }
       else {//如果定位出的股道编号和当前股道编号一致，继续在轨计算
           preLineID = curLineID;
           curLineID = curStation.Railways[min_i].PlineID;
       }
       //long t2=what_time_is_it_now();
       //printf("t2 = %d\n",t2-t1);
       //前方有两岔道时，开始计算距离，20米以后才显示；
       if (!showmap_stat && (GetDS(curTrainPos,trainSP) > 25)) {
            showmap_stat = true;
       }

       //以下计算机车BDS坐标用原坐标在轨道中心线的垂足坐标代替
       //计算机车距轨道起点的距离，单位米
       if(cur_foot_index==23){
           int test=1;
           test=2;
       }
       float per = find_disp_to_begin(curStation,trainFoot, curLineID, cur_foot_index);
       //此处要修正per，缺函数
       //??????????????????

       PutLinePer(per);
       if(inlinedataid<6) inlinedataid = inlinedataid + 1;
       if(inlinedataid>5){ //确定机车在轨道上的运行方向
           Check_Per_Direct();
           if (check_if_ok()) {//机车运行切换方向 必须连续5次判断一致
               pre_trainonlinedirect = trainonlinedirect;
               trainonlinedirect = train_line_direct[0];
           }
       }
       //long t3=what_time_is_it_now();
       //printf("t3 = %d\n",t3-t2);
       //确定机车前方进路和防控项点
       FindtheWayandPoint(curStation,per, curLineID,trainonlinedirect);

#if TESTStat
       TrainInfo.direction=trainonlinedirect;
       TrainInfo.LinePer = per / curStation.Railways[curLineID].LineLength;
       strcpy(TrainInfo.LineName,curStation.Railways[curLineID].line_name);
       if(Global_Var.KeyPointS.size()>0){
           TrainInfo.PointType = Global_Var.KeyPointS[0].PointType;
           TrainInfo.DistoP    = Global_Var.KeyPointS[0].PointDis/100.0;
           strcpy(TrainInfo.LampName,Global_Var.KeyPointS[0].PointName);
       }else if(Global_Var.KeyPointS.size()==0){
           TrainInfo.PointType=6;
           if(trainonlinedirect==1)
               TrainInfo.DistoP=curStation.Railways[curLineID].LineLength-per;
           else if(trainonlinedirect==2)
               TrainInfo.DistoP = per;
       }
       //long t4=what_time_is_it_now();
       //printf("t4 = %d\n",t4-t3);
       //printf("a = %d\n",t4-t0);
       //////////////////////test////////////////////////////////////////////////////
       byte bbuf[1024]={0};
       int  tlen=0;
       int  tpn=Global_Var.KeyPoint_N;
       if(tpn>1) tpn=1;
       if(tpn==1){
          bbuf[tlen++]=1;
          //进路上一个防控项点
          bbuf[tlen++] = Global_Var.KeyPointS[0].PointType;
          bbuf[tlen++] = Global_Var.KeyPointS[0].PointID ;
          bbuf[tlen++] = Global_Var.KeyPointS[0].PointID >>8 ;
          for( int j=0;j<12;j++){
             bbuf[tlen+j]= Global_Var.KeyPointS[0].PointName[j];
          }
          tlen=tlen+12;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointV;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointV >> 8;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointV >> 16;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointV >> 24;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointDis;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointDis >> 8;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointDis >> 16;
          bbuf[tlen++]=Global_Var.KeyPointS[0].PointDis >> 24;
       }else if(tpn ==0){
          bbuf[tlen++]=0;
       }

       bbuf[tlen++]=curLineID;         //机车所在股道序号
       bbuf[tlen++]=curLineID>>8;
       for(int j=0;j<30;j++){                                         //机车所在股道名称
          bbuf[tlen+j]= curStation.Railways[curLineID].line_name[j];
       }
       tlen=tlen+30;
       byte * pfloat;
       float map_pos=TrainInfo.LinePer;
       pfloat=(byte *)&map_pos;
       for(int i=0;i<4;i++){           //机车在股道上的显示位置
          bbuf[tlen++]=pfloat[i];
       }
       bbuf[tlen++]=0;//机车前方进路股道数量

       /////////////////////test/////////////////////////////////////////////////////
       if(showmap_stat)
          SendAInfotoMonitor(bbuf,tlen);
          sendto(socket_send,(char*)&TrainInfo,sizeof(TrainInfo),0,(struct sockaddr *)&ClientAddrTest,sizeof(ClientAddrTest));
       printf("foot_index = %d   min_ds  = %f   per= %f   lineper= %f\n",cur_foot_index,min_ds, per,TrainInfo.LinePer);
      //printf("t4 = %d\n",what_time_is_it_now()-t1);
#endif
       //usleep(200000);//200ms 5Hz

    }
    usleep(10000);
  }
}
