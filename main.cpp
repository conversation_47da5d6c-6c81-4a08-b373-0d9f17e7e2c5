#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>

#include <errno.h>
#include <signal.h>
#include <fcntl.h>
#include <unistd.h> 
#include <arpa/inet.h> 
#include <sys/socket.h>  
#include <netinet/in.h> 
#include <termios.h>
#include <pthread.h>

#include "TaxData.h"
#include "gps.h"
#include "ntrip.h"
#include "checkip.h"
#include "wdog.h"
#include "tx2_filelog.h"
#include "gpio.h"
#include "readcfg.h"
#include "udp_to_server.h"
#include "DS_Data.h"
#include "ds_station.h"
#include "save_data.h"
#include "map_update.h"

#include "zlib.h"

extern GPS_INFO GPSData;
extern bool GPSState;
extern bool NtripFlag;

int SocToGround = 0;

//extern int ServerSoc ;
static unsigned int SecrenHeartId = 0;
static unsigned int SecrenHeartIdPre = 0;


StaticVar Global_Var;

void* Thread_TaxData(void* lParam);
void* Thread_CheckTax(void* lParam);
void* Thread_TaxLed(void* lParam);

bool CheckandGetStationMap(byte TLJ,uint16_t Station_ID, uint32_t Version);
void ServerMSGSend_test();
bool LoadStation_Map(int stationID, DStation* st_map);
void *Test_Data_For_Chesuan(void *lpPara);
void * Thread_Chesuan(void *lpPara);
void* Thread_BeatTest(void *lpPara);
void GetTestData(char *dat_file);

int main(int   argc,   char*   argv[])
{
    //获取测试数据文件
    char testfile[512];
    if(argc>1){
      strcpy(testfile,argv[1]);
    }else{
      strcpy(testfile,"xzb_test.dat");  
    }
    printf("argc=%d    %s\n",argc,testfile);
    static int comfd=-1;       //北斗模块串口句柄
    static int comHeart = -1;  //智能电源串口句柄
    system("sudo hwclock --hctosys -u -f /dev/rtc1");//加载RTC时间
    sleep(1);
    struct timeval start, end;
    gettimeofday(&start, NULL); // get the beginning time
    DelLogMsg("/var/log");
    DelLogMsg("/var/log/apt");
    DelLogMsg("/var/log/fsck");
    DelLogMsg("/var/log/lightdm");
    DelLogMsg("/var/log/lightdm");
    DelLogMsg("/var/log/ntpstats");
    DelLogMsg("/var/log/openvpn");
    DelLogMsg("/var/log/supervisor");
    GPIOInit();

    char tempbuf[512];
    Global_Var.cmd_path = get_exe_path();
    Global_Var.TimeStamp=0;
    sprintf(tempbuf,"%slog",Global_Var.cmd_path);
    rk_LogPathInitial(tempbuf); //初始化日志文件路径，启动相关线程
    sprintf(tempbuf,"%scfg/dsac.xml",Global_Var.cmd_path);
    ReadXmlConfig(tempbuf); 
    comfd = SerialInit(Global_Var.BDCom, 115200);
    if(comfd == -1)
    {
        char logstr[200];
        sprintf(logstr,"Open com %s fail.", Global_Var.BDCom);
        rk_syslog(logstr);
        return -1;
    }

    comHeart = SerialInit(Global_Var.PowerCom , 9600);
    if(comHeart == -1)
    {
        char logstr[200];
        sprintf(logstr,"Open com %s fail.", Global_Var.PowerCom);
        rk_syslog(logstr);
       return -1;
    }
    sleep(1);
    //北斗模块启用联合定位
    write(comfd,"gngga com1 0.1\r\ngnrmc com1 0.1\r\nsaveconfig\r\n",70);

    //测试数据
    GetTestData(testfile);

    UDPToServerInit();
    pthread_t check3g_tid;//检查4G通信状态
    pthread_create(&check3g_tid,NULL,Thread_Check3G,NULL);

    pthread_t test_data;//接收服务器业务数据线程
    pthread_create(&test_data,NULL,Thread_RecvFromServer,&comfd);
    
    pthread_t test_timer;//向服务器发送数据线程
    pthread_create(&test_timer,NULL,Thread_Timer,NULL);
    
    pthread_t test_discal;//机车计算线程
    pthread_create(&test_discal,NULL,Thread_CalDis,NULL);

    pthread_t test_map;//地图升级线程  
    pthread_create(&test_map,NULL,Thread_MapUpdate,NULL);

    pthread_t test_save;//记录数据线程
    pthread_create(&test_save,NULL,Thread_SaveData,NULL);
    //向显示屏发送心跳包
    pthread_t send_bid;
    //pthread_create(&send_bid,NULL,Thread_BeatTest,NULL);

    //接收显示屏UPD数据
    pthread_t mon_bid;
    //pthread_create(&mon_bid,NULL,Thread_GetUDPofMonitor,NULL);

    //车算
    //pthread_t test_chesuan;
    //pthread_create(&test_chesuan,NULL,Thread_Chesuan,NULL);
  

    char buf[10];
    buf[0] = 0x55;
    buf[1] = 0x88;
    buf[2] = 0x99;
    write(comHeart,buf,3);

 #if 0

    //检查Tax箱通信是否正常线程
    pthread_t CheckTax_tid;
    pthread_create(&CheckTax_tid,NULL,Thread_CheckTax,NULL);
    //获取Tax箱数据线程
    pthread_t TaxCom_tid;
    pthread_create(&TaxCom_tid,NULL,Thread_TaxData,NULL);
    //控制Tax箱状态指示灯显示线程
    pthread_t TaxLed_tid;
    pthread_create(&TaxLed_tid,NULL,Thread_TaxLed,NULL);

    if (strlen(NtripUsr) == 0 || strlen(GPSIP) == 0) {
        rk_syslog("read config faild!!!! exit!!\n");
        return -1;
    }
    //初始化差分服务器登录参数
    NtripInit(GPSIP, GPSPort, NtripUsr, "", "RTCM32-MSM");
	//获取北斗模块数据
    pthread_t GPSTid;
    pthread_create(&GPSTid,NULL,Thread_GPS,&comfd);
    //和差分服务器交互线程
    pthread_t NtripTid;
    pthread_create(&NtripTid,NULL,Thread_NTRIP,&comfd);
	
    //pthread_t wdog_tid;
    //pthread_create(&wdog_tid,NULL,Thread_wdog,NULL);//开启看门狗
 #endif


	while(1)
	{
          //智能电源串口送数
          write(comHeart,buf,3);
//        if(GPSState && GPSData.State != 0 ){//按北斗时间校时
//           struct tm *t;
//           time_t tt;
//           time(&tt);
//           t=localtime(&tt);
//           if( ((t->tm_min != GPSData.GTime.minute) ||(t->tm_sec != GPSData.GTime.second))
//                          && GPSData.GTime.month >0
//                          && GPSData.GTime.month <13
//                          && GPSData.GTime.year  >20
//                          && GPSData.GTime.day   >0
//                          && GPSData.GTime.day   <32
//                          && GPSData.GTime.hour  >=0
//                          && GPSData.GTime.hour  <24
//                          && GPSData.GTime.minute >=0
//                          && GPSData.GTime.minute <60
//                          && GPSData.GTime.second >=0
//                          && GPSData.GTime.second <60 ){
//                    FILE *fp;
//                    char cmdstr[500];
//                    sprintf(cmdstr,"sudo date -s '%d/%d/%d %d:%d:%d'",GPSData.GTime.year+2000
//                                                                     ,GPSData.GTime.month
//                                                                     ,GPSData.GTime.day
//                                                                     ,GPSData.GTime.hour
//                                                                     ,GPSData.GTime.minute
//                                                                     ,GPSData.GTime.second);
//                    fp=popen(cmdstr,"r");
//                    pclose(fp);
//                    sleep(1);
//                    fp=popen("hwclock --systohc -u -f /dev/rtc1","r");
//                    pclose(fp);
//                    rk_syslog("按北斗时间校时");
//           }

//        }
        sleep(10);
    }
    return 0;
}


