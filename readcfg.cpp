#include "DS_Data.h"
#include "tinyxml2.h"
#include "tx2_filelog.h"

extern StaticVar Global_Var;


char NtripUsr[128] = "";
int  GPSPort = 0;
char GPSIP[28] = "";
char SERIP[28] = "";
char ClientAIP[28] = "*************";
char ClientBIP[28] = "*************";


unsigned short DistoA =0,DistoB=0;
unsigned char RunType=1;
unsigned char CtrlStat=0;
unsigned char CheSuan=0;



char *makeword(char *line, char stop)
{
    int x = 0, y;
    char *word = (char *)malloc(sizeof(char)*(strlen(line) + 1));
    for (x = 0; ((line[x]) && (line[x] != stop)); x++)
        word[x] = line[x];
    word[x] = '\0';
    if (line[x]) ++x;
    y = 0;
    while (line[y++] = line[x++]);
    return word;
}




int ReadXmlConfig(char *xmlFile)
{

   for(int i=0;i<32;i++){
        Global_Var.Train_ID[i]=0;
   }

   tinyxml2::XMLDocument doc;
   tinyxml2::XMLError ret = doc.LoadFile(xmlFile);
   if (ret != 0) {
       printf("加载配置文件失败.\n");
       return -1;
   }
   tinyxml2::XMLElement* root = doc.RootElement();

   tinyxml2::XMLElement* RK3568 = root->FirstChildElement("RK3568");
   if (!RK3568) {//缺省
        strcpy(Global_Var.BDCom,"/dev/ttyS0");
        strcpy(Global_Var.PowerCom,"/dev/ttyS7");
        strcpy(Global_Var.PowerCom,"/dev/ttyS9");
        strcpy(Global_Var.RadioCom,"/dev/ttyUSB2");
        strcpy(Global_Var.DevName4G,"usb0");
   }

   tinyxml2::XMLElement* tvar = RK3568->FirstChildElement("BDCom");
   if (!tvar) {//缺省
        strcpy(Global_Var.BDCom,"/dev/ttyS0");
   }
   strcpy(Global_Var.BDCom,tvar->GetText());

   tvar = RK3568->FirstChildElement("PowerCom");
   if (!tvar) {//缺省
         strcpy(Global_Var.PowerCom,"/dev/ttyS7");
   }
   strcpy(Global_Var.PowerCom,tvar->GetText());

   tvar = RK3568->FirstChildElement("TaxCom");
   if (!tvar) {//缺省
         strcpy(Global_Var.TaxCom,"/dev/ttyS9");
   }
   strcpy(Global_Var.TaxCom,tvar->GetText());

   tvar = RK3568->FirstChildElement("RadioCom");
   if (!tvar) {//缺省
         strcpy(Global_Var.RadioCom,"/dev/ttyUSB2");
   }
   strcpy(Global_Var.RadioCom,tvar->GetText());

   tvar = RK3568->FirstChildElement("DevName4G");
   if (!tvar) {//缺省
         strcpy(Global_Var.RadioCom,"usb0");
   }
   strcpy(Global_Var.DevName4G,tvar->GetText());


   tinyxml2::XMLElement* CMServer = root->FirstChildElement("CMServer");
   if (!CMServer) {//缺省
        strcpy(Global_Var.SERIP,"***************");
        Global_Var.SERPort=10003;
   }
   tvar = CMServer->FirstChildElement("IP");
   if (!tvar) {//缺省
      strcpy(Global_Var.SERIP,"***************");
   }
   strcpy(Global_Var.SERIP,tvar->GetText());
   
   tvar = CMServer->FirstChildElement("UDPPort");
   if (!tvar) {//缺省
        Global_Var.SERPort=10003;
   }
   Global_Var.SERPort=atoi(tvar->GetText());

   tinyxml2::XMLElement* Train = root->FirstChildElement("Train");
   if (!Train) {//缺省
        Global_Var.DistanceA=0;;
        Global_Var.DistanceB=0;;
   }
   tvar = Train->FirstChildElement("DistanceA");
   if (!tvar) {//缺省
      Global_Var.DistanceA=0;
   }
   Global_Var.DistanceA=atoi(tvar->GetText());

   tvar = Train->FirstChildElement("DistanceB");
   if (!tvar) {//缺省
      Global_Var.DistanceB=0;
   }
   Global_Var.DistanceB=atoi(tvar->GetText());

   tvar = Train->FirstChildElement("IDentity");
   if (!tvar) {//缺省
     rk_syslog("缺少机车认证码.");
   }else{
     for(int i=0;i<strlen(tvar->GetText());i++){
        Global_Var.Train_ID[i]=tvar->GetText()[i];
     }
   }
  
 
    return 0;
}