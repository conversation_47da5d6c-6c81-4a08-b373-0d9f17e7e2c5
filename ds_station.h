#pragma once
#include <vector>
#include "TaxData.h"
#define USHORT unsigned short

#pragma pack(push)
#pragma pack(1)
//pack(1)老版本数据结构  用于测试,调试
typedef struct _HeadPos {
    unsigned char flg;       //数据有效 0 无效 1 有效
    unsigned char headType;  //火车端头  A端   B端
    unsigned char bRunHead;  //是否是前进车头 0 不是 1 是
    double hx;               //火车端头垂坐标
    double hy;               //火车端头垂坐标
    _HeadPos() {
        flg = 0;
        headType = 0;
        bRunHead = 0;
        hx = 0;
        hy = 0;
    }
public:
    _HeadPos &operator=(const _HeadPos &temp) {
        if (this == &temp) {
            return *this;
        }
        flg = temp.flg;
        headType = temp.headType;
        hx = temp.hx;
        hy = temp.hy;
        return *this;
    }
}HeadPos;

typedef struct _TrainSendInfo  //向地面传送的机车实时信息数据结构
{
    USHORT          header;                  //头信息
    USHORT          length;                  //报文长度
    USHORT          signtype;                //信息类别编码  1;
    unsigned char   StationID;               //站场ID
    unsigned char   PlineID;                 //当前线路编号
    unsigned char   HeadType;                //A节或B节  0--A  1--B
    unsigned char   TAXStat;                 //TAX状态
    unsigned char   GPSStat;                 //GPS状态
    double          tx;                      //当前火车WGS84坐标
    double          ty;                      //当前火车WGS84坐标
    double          longitude;               //当前火车经度
    double          latitude;                //当前火车纬度
    unsigned char   direction;               //火车方向  1:起点-->终点  2:起点<--终点
    unsigned char   PointType;               //前方防撞点类型 ，0 没有  1--信号灯  2--接触网终点 3--脱轨器  4--土挡 5--站界
    unsigned char   PointID;                 //防撞点全局索引
    float           DistoP;                  //距离当前防撞点距离
    TAXTIME         TAXTime;		         //TAX时间
    USHORT          Speed;	                 //机车速度
    USHORT          MaxSpeed;                //机车限速
    unsigned int    DriverNo;	             //司机号
    unsigned int    EngineNo;	             //机车号
    unsigned char   EngineType;              //机车型号
    float           train_direct;            //火车方向角度
    HeadPos         headPos;                 //车头标志
    ///add new ////
    char            LocoState;               //调车状态   0 非调车，1 调车
    char            LampStateValue;          //信号灯状态  1：蓝灯，2：白灯，3红灯，初始值0
    ///add new ////
    char            EngineTypeStr[20];       //机车型号
    char            TaxIsExit;               //TAX是否存在
    char            State4G;                 //4G状态
    double          LinePer;                 //火车在当前线路的百分比
    char            LineName[20];            //当前线路名字
    /** add 20210118 **/
    char            ProVersion;              //协议版本 ,起始为3，之后累加
    char            LampName[20];            //灯名字
    USHORT          winver;                  //显示屏软件版本
    USHORT          acver;                   //rk3399或tx2主机版本
    USHORT          lampver;                 //信号灯识别软件版本
    float           gps_err;                 //定位误差
    unsigned char   TrainType;               //车次字母部分
    unsigned int    TrainNum;                //车次
    char            yuliu[23];               //预留
    /** add 20210118 **/
    unsigned char   sc;                      //校验和
    _TrainSendInfo()
    {
        this->header = 0x55BB;
        this->length = sizeof(_TrainSendInfo);
        this->signtype = 1;
        PlineID = -1;
        LocoState = 0;
        LampStateValue = 0;
        LinePer = 0;
        memset(EngineTypeStr, 0x00, sizeof(EngineTypeStr));
        memset(LineName, 0x00, sizeof(LineName));
        memset(LampName, 0x00, sizeof(LampName));
        memset(yuliu, 0x00, sizeof(yuliu));
        State4G = 0;
        winver = 0;
        acver = 0;
        TaxIsExit = 0;
        State4G = 0;
        PointType = 0;
        ProVersion = 3;
        sc = 0;
    }
}TrainSendInfo;

#pragma pack(pop)


typedef struct _DSRect
{
	float xmin;      
	float xmax;
	float ymin;
	float ymax;
}DSRect;


typedef struct _DSTrain
{
	char TrType[12];
	unsigned short TrID;
}DSTrain;

typedef struct _DSLamp //信号灯数据类型
{
	unsigned short    lamp_index;      //信号灯全局Index;
	unsigned char       lamptype;      //信号灯方向  1面向起点，2面向终点
	DSPoint             position;      //信号灯垂足位置坐标
	DSPoint            actualPos;      //信号灯实际位置坐标
	char           lamp_name[20];      //信号灯名称
	unsigned char      lampvalue;      //信号灯状态值,1：蓝灯，2：白灯，3红灯
	unsigned char       lampkind;      //信号灯种类，2：两灯,3：三灯，5：五灯，7：七灯
	unsigned char  lampleftRight;      //信号灯左右位置，0：左，1：右
	unsigned char     lampheight;      //信号灯高度，0：矮杆，1：高杆
	float                linepos;      //信号灯在线路上的位置，即距起点的距离

	_DSLamp() {
		lamp_index = 0;
		lamptype = 0;
		memset(lamp_name, 0x00, sizeof(lamp_name));
		lampvalue = 0;
		lampkind = 2;
		lampleftRight = 0;
		lampheight = 0;
	}
}DSLamp;

typedef struct _DSNoParkingArea   //禁停区数据类型
{
	unsigned short   NPA_index;     //禁停区全局索引
	DSPoint          begin_pos;     //禁停区起点，靠近线路起点
	DSPoint            end_pos;     //禁停区终点，靠近线路终点
	float        begin_linepos;     //禁停区起点在线路上的位置，即距线路起点的距离
	float          end_linepos;     //禁停区终点在线路上的位置，即距线路起点的距离
}DSNoParkingArea;

typedef struct _DSTurnout  //道岔数据类型
{
	unsigned short     t_id;       //道岔Index
	char        to_name[12];       //道岔名称
	DSPoint        position;       //道岔坐标
	unsigned char      stat;       //道岔状态
}DSTurnout;

typedef struct _DStoppoint  //土档终点线数据类型
{
	unsigned short        s_id;        //土挡Index
	unsigned char        Stype;        //土挡方向  1 面向线路起点  2 面向线路终点 
	char    Stoppoint_name[12];        //土挡终点线名称
	DSPoint           position;        //土挡坐标
	float             line_pos;        //土挡在线路位置 
}DStoppoint;

typedef struct _DSPowerEnd      //接触网终点标数据类型
{
	unsigned short     PowerEnd_index;   //接触网终点标全局Index;
	unsigned char      PowerEnd_type;    //接触网终点标类型  1 面向线路起点  2 面向线路终点 
	DSPoint                 position;    //接触网终点标位置坐标
	float                    linepos;    //接触网终点标在线路位置
}DSPowerEnd;

typedef struct _DSDerailer    //脱轨器数据数据类型
{
	unsigned short    Derailer_index;   //脱轨器全局Index;
	unsigned char      Derailer_type;   //脱轨器类型  1 面向线路起点  2 面向线路终点  
	DSPoint                 position;   //脱轨器位置坐标
	float                    linepos;   //脱轨器在线路位置
}DSDerailer;

typedef struct _DStationLimit  //站界数据类型
{
	unsigned short    StLimit_index;   //站界全局Index;
	unsigned char      StLimit_type;   //站界类型  1 面向线路起点  2 面向线路终点   
	DSPoint                position;   //站界位置坐标
	float                   linepos;   //站界在线路位置
}DStationLimit;

typedef struct _DSpecialPoint  //特殊防控项点数据类型
{
	unsigned short        sp_index;    //全局Index;
    unsigned char          sp_type;    //特殊防控项点类型   0  双向 1 面向线路起点  2 面向线路终点
	char                SPName[12];    //特殊防控项点名称（界面显示）
	char             speechtxt[50];    //语音提示文本
	DSPoint               position;    //位置坐标
	float                  linepos;    //站界在线路位置
}DSpecialPoint;

typedef struct _DSPline
{
	unsigned short                      PlineID;       //线路序号
	char                          line_name[20];       //线路名称
	unsigned char                       up_type;       // 1 起点->终点为上行,
	                                                   // 2 起点->终点为下行
	                                                   // 0 未定   
	unsigned char                  NonPowerStat;       //无电区线路标志
	                                                   // 1 无电  0 有电
	float		                     LineLength;       //线路总长
	unsigned char                      preLineN;       //头线路数量
    std::vector<USHORT>              preLineIDs;       //头线路容器
	unsigned char                     nextLineN;       //尾线路数量
    std::vector<USHORT>             nextLineIDs;       //尾线路容器
    USHORT                         totalPointsN;       //总点数
    std::vector<DSPoint>                 Points;        //点坐标容器
	unsigned char                    totalLampN;       //线路上信号灯数量
    std::vector<USHORT>                   Lamps;       //信号灯容器
	unsigned char                 totalTurnoutN;       //线路上道岔的数量
    std::vector<USHORT>                Turnouts;       //道岔容器
	unsigned char                totalPowerEndN;       //线路上接触网终标点数量
    std::vector<USHORT>               Powerends;       //接触网终点标容器
	unsigned char               totalStoppointN;       //土挡数量  
    std::vector<USHORT>              Stoppoints;       //土挡容器
	unsigned char                totalDerailerN;       //脱轨器数量
    std::vector<USHORT>               Derailers;       //脱轨器容器
	unsigned char                 totalStLimitN;       //站界数量
    std::vector<USHORT>                StLimits;       //站界容器
	unsigned char                     totalNPAN;       //禁停区数量 
    std::vector<USHORT>                    NPAs;       //禁停区容器
	unsigned char                      totalSPN;       //特殊防控项点数量
    std::vector<USHORT>              SpecPoints;       //特殊防控项点容器
	unsigned char                   totalTrainN;       //线路上机车数量
    std::vector<DSTrain>                 Trains;       //机车容器
	_DSPline() {
		PlineID = -1;
		LineLength = 0;
		up_type = 0;
		NonPowerStat = 0;
		preLineN = 0;
		nextLineN = 0;
		totalPointsN = 0;
		totalLampN = 0;
		totalTurnoutN = 0;
		totalPowerEndN = 0;
		totalStoppointN = 0;
		totalDerailerN = 0;
		totalStLimitN = 0;
		totalNPAN = 0;
		totalSPN = 0;
		totalTrainN = 0;
	}
}DSPline;

typedef struct _DStation   //站场数据结构
{
	unsigned char                      areaID;       //铁路局ID
	char                         areaName[20];       //铁路局名称
	unsigned short                  stationID;       //站场ID
	char                      stationName[30];       //站场名称
	unsigned char                 stationType;       //站场类型  0 无站场信号  1 TDCS  2 股道自动化
	DSRect                           rectArea;       //站场矩形范围  投影坐标
	unsigned short                 pyPoints_N;       //站场多边形范围点数  
	std::vector<DSPoint>             pyPoints;       //站场多边形范围边界点容器(经纬度) 
	unsigned short                  Turnout_N;       //站场道岔数量 
	std::vector<DSTurnout>           Turnouts;       //道岔容器
	unsigned short                     Lamp_N;       //站场信号灯数量
	std::vector<DSLamp>                 Lamps;       //信号灯容器
	unsigned short                 PowerEnd_N;       //站场接触网终标点数量
	std::vector<DSPowerEnd>         Powerends;       //接触网终点标容器
	unsigned short                 Derailer_N;       //站场脱轨器数量
	std::vector<DSDerailer>         Derailers;       //脱轨器容器
	unsigned short                Stoppoint_N;       //站场土挡数量  
	std::vector<DStoppoint>        Stoppoints;       //土挡容器 
	unsigned short                  StLimit_N;       //站场站界数量
	std::vector<DStationLimit>       StLimits;       //站界容器
	unsigned short                      NPA_N;       //站场禁停区数量 
	std::vector<DSNoParkingArea>         NPAs;       //禁停区容器 
	unsigned short                SpecPoint_N;       //站场特殊防控项点数量
	std::vector<DSpecialPoint>     SpecPoints;       //特殊防控项点容器  
	unsigned short                     Rail_N;       //站场线路数量
	std::vector<DSPline>             Railways;       //线路容器    
}DStation;

