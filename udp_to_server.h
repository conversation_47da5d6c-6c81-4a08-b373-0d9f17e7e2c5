#ifndef UDP_TOSERVER_H
#define UDP_TOSERVER_H
extern int UServerSoc;
extern struct sockaddr_in ServerAddr;
int UDPToServerInit();
void * Thread_Timer(void *lpPara);
long what_time_is_it_now();
int  SendDataToSer(unsigned char* buf,unsigned long plen,int zstat);
int  CheckifinStation(float lon,float lat);
void* Thread_GetUDPofMonitor(void *lpPara);
void* Thread_RecvFromServer(void *lpPara);
void* Thread_CalDis(void *lpPara);
#endif // UDP_TOSERVER_H
