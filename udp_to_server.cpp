﻿#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <fcntl.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/socket.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <iconv.h>
#include <vector>

#include "zlib.h"
#include "TaxData.h"
#include "DS_Data.h"
#include "gps.h"
#include "crc.h"
#include "readcfg.h"
#include "gpio.h"
#include "tx2_filelog.h"
#include "sm4.h"

#include "save_data.h"

#include "station_data.h"//测试用

int UServerSoc = -1;
struct sockaddr_in ServerAddr;
uint16_t sMSGId = 0;//服务器msgid
uint16_t FreamNum = 0;
byte MonA_Stat=0,MonB_Stat=0;

extern TAXDATA curTaxData;
extern bool TaxStat;
extern GPS_INFO GPSData;
extern StaticVar Global_Var;

struct sockaddr_in ClientAddr1;
struct sockaddr_in ClientAddr2;


queue<MapDataBuf> MapDatQueue;//站场图升级用
mutex mtxMapDatQueue;

mutex mtxFream;  

mutex mtxOpTy;
queue <int> OpType;//操作类型队列
                  //  0         没有  
                  //  1         第一次15公里/小时车速控制
                  //  2         超过7秒后速度没降下来控停
                  //  3         第一次控停
                  //  4         第二次控停   
                  //  5-9       保留 
                  //  10--29    菜单操作
                  //  30--255   30+语音ID


byte  preSpeed15Shutdown = 0;    //第一次15公里/小时车速控制
byte  preSpeed15Shutdown_7s = 0; //超过7秒后速度没降下来停车
long first_stop;
int preShutdown = -1;
int preShutdown_3m = 0;

extern vector<TrSentData> v_sentbuf;//测试用 ，模拟数据

Sm4EncDec de(1);

//bool pnpoly(int nvert, float* vertx, float* verty, float testx, float testy)
//{
//    int i, j;
//    bool c = false;
//    for (i = 0, j = nvert - 1; i < nvert; j = i++) {
//        if (((verty[i] > testy) != (verty[j] > testy))  &&
//            (testx < (vertx[j] - vertx[i]) * (testy - verty[i]) / (verty[j] - verty[i]) + vertx[i]))
//            c = !c;
//    }
//    return c;
//}

void PushOpType(int otype)
{
    mtxOpTy.lock();
    OpType.push(otype);
    mtxOpTy.unlock();
}

int GetOpType()
{
   int stat=0;
   mtxOpTy.lock();
   if(!OpType.empty()){
        stat=OpType.front();
        OpType.pop();
   }
   mtxOpTy.unlock();
   return stat;
}


bool pnpoly(int nvert, DSPoint* points, float testx, float testy)
{
    int i, j;
    bool c = false;
    for (i = 0, j = nvert - 1; i < nvert; j = i++) {
        if (((points[i].y > testy) != (points[j].y > testy))  &&
            (testx < (points[j].x - points[i].x) * (testy - points[i].y) / (points[j].y - points[i].y) + points[i].x))
            c = !c;
    }
    return c;
}



long what_time_is_it_now()
{
    long res;
    struct timeval time;
    if (gettimeofday(&time,NULL)){
        return 0;
    }
    res = time.tv_sec*1000 + time.tv_usec*0.001;
    return res;
}

//服务器网络接口初始化
int UDPToServerInit()
{
        memset(&ServerAddr, 0, sizeof(ServerAddr));
        ServerAddr.sin_family = AF_INET;
        ServerAddr.sin_port = htons(Global_Var.SERPort);
        ServerAddr.sin_addr.s_addr = inet_addr(Global_Var.SERIP);

        if ((UServerSoc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
        {
                rk_syslog("Server Socket Error.");
                return -1;
        }

        return 0;
}


int DataDLEDealFun(unsigned char* Dst,unsigned char* Src,int len,char flag)
{
        if (len < 2)
        {
                return -1;
        }

        int pos = 0;

        if (flag == 0)
        {
                for (int i = 0;i < len;i++)
                {
                        if (Src[i] == 0x10 && i > 1 && i< len -2)
                        {
                                Dst[pos] = 0x10;
                                pos ++;
                        }
                        Dst[pos] = Src[i];
                        pos ++;
                }
        }
        else if (flag == 1)
        {
                for (int i = 0;i < len;i++)
                {
                        if (Src[i] == 0x10 && i > 1 && i< len -2)
                        {
                                i++;
                        }
                        Dst[pos] = Src[i];
                        pos ++;
                }
        }
        return pos;
}


//向服务器发送车载数据功能函数
//  zstat: 0：无压缩 1：压缩（默认方式zlib）2：压缩（原始5A5A开头数据需SM4加密）
int  SendDataToSer(byte* buf,unsigned long plen,int zstat)
{
    unsigned char SendBuf[512] = "";
    unsigned long len;
    SendBuf[0] = 0x5A;
    SendBuf[1] = 0x5A;
    len=plen+12;
    SendBuf[2] = len;
    SendBuf[3] = len >> 8;
    SendBuf[4] = 0x05;//协议版本
    mtxFream.lock();
    FreamNum++;
    mtxFream.unlock();
    SendBuf[6] = FreamNum;
    SendBuf[7] = FreamNum >> 8;
    memcpy(&SendBuf[8],buf,plen);

    uint32_t crc = CRC32(SendBuf,len-4);
    SendBuf[len -4] = crc;
    SendBuf[len -3] = crc >> 8;
    SendBuf[len -2] = crc >> 16;
    SendBuf[len -1] = crc >> 24;

    unsigned char zSendBuf[800] = "";
    zSendBuf[0] = zstat;
    zSendBuf[2] = len;
    zSendBuf[3] = len>>8;
    zSendBuf[4] = len;
    zSendBuf[5] = len>>8;

    unsigned long zlen;
    if(zstat==0){//无压缩
        memcpy(&zSendBuf[6],SendBuf,len);
        zlen=len;
    }else if(zstat==1){//压缩（方式zlib）
        zlen = sizeof(zSendBuf)-6;
        compress(&zSendBuf[6],&zlen,SendBuf,len);
    }else if(zstat==2){//压缩（原始5A5A开头数据需SM4加密）
        unsigned char buf1[1024] = {0};
        int el = de.Encrypt(buf1,SendBuf,len);
        zSendBuf[2] = el;
        zSendBuf[3] = el>>8;
        zlen = sizeof(zSendBuf)-6;
        compress(&zSendBuf[6],&zlen,buf1,el);
    }
    unsigned char GSMRBuf[1024] = "";
    GSMRBuf[0] = 0x10;
    GSMRBuf[1] = 0x02;

    // zlen 车载发送数据包长度
    // 17 表示从“源端口代码”开始到“CRC”结束的字节数
    // 6 为车载发送数据包上面的6个字节数

    // 信息长度 大端
    GSMRBuf[2] = (17+6+zlen) >> 8;
    GSMRBuf[3] = (17+6+zlen);

    // 源端口代码
    GSMRBuf[4] = 0x36;
    // 源通信地址长度
    GSMRBuf[5] = 0x04;

    //源通信地址
    GSMRBuf[6] = 65;//源通讯地址 000

    // 目的端口代码
    GSMRBuf[10] = 0x38;
    // 目的通信地址长度
    GSMRBuf[11] = 0x04;

    
    GSMRBuf[16] = 0x30;
    GSMRBuf[17] = 0x04;
    GSMRBuf[18] = 0xFF;//业务数据

    memcpy(&GSMRBuf[19],zSendBuf,zlen+6);

    uint16_t GCrc = GetCRC16((char*)(GSMRBuf+2),(17+6+zlen));

    GSMRBuf[0x17+2+zlen] = GCrc >> 8;
    GSMRBuf[0x17+3+zlen] = GCrc;
    GSMRBuf[0x17+4+zlen] = 0x10;
    GSMRBuf[0x17+5+zlen] = 0x03;

    unsigned char DLESendBuf[1024] = "";
    int DLElen = DataDLEDealFun(DLESendBuf,GSMRBuf,0x17+6+zlen,0);
    return sendto(UServerSoc,(char*)DLESendBuf,DLElen,0,(struct sockaddr *)&ServerAddr,sizeof(ServerAddr));
}


void  SendDataToSer_test(byte* buf,unsigned long plen,int zstat)
{
    unsigned char SendBuf[512] = "";
    unsigned long len;
    SendBuf[0] = 0x5A;
    SendBuf[1] = 0x5A;
    len=plen+12;
    SendBuf[2] = len;
    SendBuf[3] = len >> 8;
    SendBuf[4] = 0x05;//协议版本
    SendBuf[6] = 1;
    SendBuf[7] = 1 >> 8;
    memcpy(&SendBuf[8],buf,plen);

    uint32_t crc = CRC32(SendBuf,len-4);
    SendBuf[len -4] = crc;
    SendBuf[len -3] = crc >> 8;
    SendBuf[len -2] = crc >> 16;
    SendBuf[len -1] = crc >> 24;

    unsigned char zSendBuf[800] = "";
    zSendBuf[0] = zstat;
    zSendBuf[2] = len;
    zSendBuf[3] = len>>8;
    zSendBuf[4] = len;
    zSendBuf[5] = len>>8;

    unsigned long zlen;
    char filename[100];
    if(zstat==0){//无压缩
        memcpy(&zSendBuf[6],SendBuf,len);
        zlen=len;
        strcpy(filename,"/home/<USER>/test_0.dat");
    }else if(zstat==1){//压缩（方式zlib）
        zlen = sizeof(zSendBuf)-6;
        compress(&zSendBuf[6],&zlen,SendBuf,len);
        strcpy(filename,"/home/<USER>/test_1.dat");
    }else if(zstat==2){//压缩（原始5A5A开头数据需SM4加密）
        unsigned char buf1[1024] = {0};
        int el = de.Encrypt(buf1,SendBuf,len);
        zSendBuf[2] = el;
        zSendBuf[3] = el>>8;
        zlen = sizeof(zSendBuf)-6;
        compress(&zSendBuf[6],&zlen,buf1,el);
        strcpy(filename,"/home/<USER>/test_2.dat");
    }
    unsigned char GSMRBuf[1024] = "";
    GSMRBuf[0] = 0x10;
    GSMRBuf[1] = 0x02;

    GSMRBuf[2] = (17+6+zlen) >> 8;
    GSMRBuf[3] = (17+6+zlen);

    GSMRBuf[4] = 0x36;
    GSMRBuf[5] = 0x04;

    GSMRBuf[6] = 65;//源通讯地址 000

    GSMRBuf[10] = 0x38;
    GSMRBuf[11] = 0x04;

    GSMRBuf[16] = 0x30;
    GSMRBuf[17] = 0x04;
    GSMRBuf[18] = 0xFF;//业务数据

    memcpy(&GSMRBuf[19],zSendBuf,zlen+6);

    uint16_t GCrc = GetCRC16((char*)(GSMRBuf+2),(17+6+zlen));

    GSMRBuf[0x17+2+zlen] = GCrc >> 8;
    GSMRBuf[0x17+3+zlen] = GCrc;
    GSMRBuf[0x17+4+zlen] = 0x10;
    GSMRBuf[0x17+5+zlen] = 0x03;

    unsigned char DLESendBuf[1024] = "";
    int DLElen = DataDLEDealFun(DLESendBuf,GSMRBuf,0x17+6+zlen,0);
    FILE *fp;
    fp =fopen(filename,"wb");
    fwrite(DLESendBuf,1,DLElen,fp);
    fclose(fp);
    return ;
}


//向服务器发送车载数据
int ServerMSGSend()
{

    unsigned char SendBuf[1024] = "";
    unsigned long len = sizeof(BASEMSG) + sizeof(BDSMSG) + sizeof(TAXMSG);

    BASEMSG BaseMsg;
    BaseMsg.IDentity[0] = 'L';
    BaseMsg.IDentity[1] = 'T';
    BaseMsg.IDentity[2] = '-';
    BaseMsg.IDentity[3] = '7';
    BaseMsg.IDentity[4] = '7';
    BaseMsg.IDentity[5] = '7';
    BaseMsg.IDentity[6] = '8';
    BaseMsg.IDentity[7] = 0;
    BaseMsg.IDentity[8] = 0;
    BaseMsg.TrainNumber[0] = curTaxData.EngineType;
    BaseMsg.TrainNumber[1] = 0;

    BaseMsg.TrainNumber[2] = curTaxData.EngineNo;
    BaseMsg.TrainNumber[3] = curTaxData.EngineNo>>8;
    BaseMsg.TrainNumber[4] = curTaxData.EngineNo>>16;
    BaseMsg.TrainNumber[5] = curTaxData.EngineNo>>24;

    BaseMsg.TrainNumber[7] = 1;


    BaseMsg.TLJ = 0x06;//郑州
    BaseMsg.Sectionno = 0x09;
    time_t tt;
    time(&tt);
    BaseMsg.TimeStamp=tt;
    memcpy(&SendBuf[0],&BaseMsg,sizeof(BASEMSG));

    BDSMSG GpsMsg;
    GpsMsg.MSGID = what_time_is_it_now();

    GpsMsg.Longitude = GPSData.longitude*10000000;
    GpsMsg.Latitude = GPSData.latitude*10000000;

    GpsMsg.Speed = GPSData.speed;
    GpsMsg.Flag = GPSData.State;


    GpsMsg.BaseStID = GPSData.BaseStationID;
    GpsMsg.BaseStTime = GPSData.BaseStationTime;
    GpsMsg.StarNum = GPSData.StarNum;
    GpsMsg.TypeID = curTaxData.EngineType;  //预留
    GpsMsg.TrainN = curTaxData.EngineNo;    //预留
    memcpy(&SendBuf[0+sizeof(BASEMSG)],&GpsMsg,sizeof(BDSMSG));


    TAXMSG LkjMsg;
    if (TaxStat)//30s内 有数据
    {
        LkjMsg.IsCanUse = 1;
    }
    LkjMsg.Speed = curTaxData.Speed;
    if ((curTaxData.TrainState & 0x06) == 2)//back
    {
        LkjMsg.TrainDirect = 16;
    }
    else if ((curTaxData.TrainState & 0x06) == 4)//forw
    {
        LkjMsg.TrainDirect = 1;
    }


    LkjMsg.TrainCondition = curTaxData.TrainState;
    LkjMsg.EquipStatus = Global_Var.EquipStatus;
    LkjMsg.TrainNumber[0] = curTaxData.TrainType;
    LkjMsg.TrainNumber[4] = curTaxData.TrainNum;
    LkjMsg.TrainNumber[5] = curTaxData.TrainNum >>8;
    LkjMsg.TrainNumber[6] = curTaxData.TrainNum >>16;
    LkjMsg.TrainNumber[7] = curTaxData.TrainNum >>24;
    LkjMsg.Distance = Global_Var.dis;
    LkjMsg.DistanceA=DistoA;
    LkjMsg.DistanceB=DistoB;
    LkjMsg.UseMSGID = sMSGId;

    if(Global_Var.TAX_Stat==1){
       LkjMsg.ErrorNumber=LkjMsg.ErrorNumber & 0xFC;
    }else{
       LkjMsg.ErrorNumber=LkjMsg.ErrorNumber | 0X01;
    }
    if(Global_Var.BDS_Stat==0){
       LkjMsg.ErrorNumber=LkjMsg.ErrorNumber | 0x04;
    }else{
       LkjMsg.ErrorNumber=LkjMsg.ErrorNumber & 0xF3;
    }

    memcpy(&SendBuf[0+sizeof(BASEMSG)+sizeof(BDSMSG)],&LkjMsg,sizeof(TAXMSG));
    return SendDataToSer(SendBuf,len,1);
}
//向服务器发送车载数据测试用
void ServerMSGSend_test(unsigned int index)
{
   if(index*10>v_sentbuf.size()) return;

    unsigned char SendBuf[1024] = "";
    unsigned long len = sizeof(BASEMSG) + sizeof(BDSMSG) + sizeof(TAXMSG);
    BASEMSG BaseMsg;
    BaseMsg.IDentity[0] = 'L';
    BaseMsg.IDentity[1] = 'T';
    BaseMsg.IDentity[2] = '-';
    BaseMsg.IDentity[3] = '7';
    BaseMsg.IDentity[4] = '7';
    BaseMsg.IDentity[5] = '7';
    BaseMsg.IDentity[6] = '8';
    BaseMsg.IDentity[7] = 0;
    BaseMsg.IDentity[8] = 0;
    for(int i=9;i<32;i++){
       BaseMsg.IDentity[i]=0;
    }

    BaseMsg.TrainNumber[0] = 237;
    BaseMsg.TrainNumber[1] = 0;

    BaseMsg.TrainNumber[2] = 1433;
    BaseMsg.TrainNumber[3] = 1433>>8;
    BaseMsg.TrainNumber[4] = 1433>>16;
    BaseMsg.TrainNumber[5] = 1433>>24;
    BaseMsg.TrainNumber[7] = 1;


    BaseMsg.TLJ = 0x01;//
    BaseMsg.Sectionno = 0x07;
    
    BaseMsg.TimeStamp=time(NULL);
   
    memcpy(&SendBuf[0],&BaseMsg,sizeof(BASEMSG));

    BDSMSG GpsMsg;
    GpsMsg.MSGID = what_time_is_it_now();

    GpsMsg.Longitude = v_sentbuf[index*10].tinfo.longitude*10000000;
    GpsMsg.Latitude = v_sentbuf[index*10].tinfo.latitude*10000000;

    GpsMsg.Speed = v_sentbuf[index*10].tinfo.Speed;
    GpsMsg.Flag = 4;


    GpsMsg.BaseStID = 13;
    GpsMsg.BaseStTime = 9999;
    GpsMsg.StarNum = 24;
    GpsMsg.TypeID = 237;
    GpsMsg.TrainN = 1433;
    memcpy(&SendBuf[0+sizeof(BASEMSG)],&GpsMsg,sizeof(BDSMSG));


    TAXMSG LkjMsg;
    LkjMsg.IsCanUse = 1;

    LkjMsg.Speed = v_sentbuf[index*10].tinfo.Speed;
    LkjMsg.TrainDirect = 16;



    LkjMsg.TrainCondition = 1;
    LkjMsg.TrainNumber[0]= 'A';
    LkjMsg.TrainNumber[1]= 'H';
    LkjMsg.TrainNumber[2]= '\0';
    LkjMsg.TrainNumber[2]= 0;
    LkjMsg.TrainNumber[4] = 1933;
    LkjMsg.TrainNumber[5] = 1933 >>8;
    LkjMsg.TrainNumber[6] = 1933 >>16;
    LkjMsg.TrainNumber[7] = 1933 >>24;

    LkjMsg.DistanceA=1;
    LkjMsg.DistanceB=26;
    LkjMsg.UseMSGID = 1111;

    memcpy(&SendBuf[0+sizeof(BASEMSG)+sizeof(BDSMSG)],&LkjMsg,sizeof(TAXMSG));
    int z= SendDataToSer(SendBuf,len,1);
    curTaxData.Speed = v_sentbuf[index*10].tinfo.Speed;
    curTaxData.MaxSpeed = v_sentbuf[index*10].tinfo.MaxSpeed;
    Global_Var.Speed = curTaxData.Speed;
    
    //SendDataToSer_test(SendBuf,len,0);
    //SendDataToSer_test(SendBuf,len,1);
    //SendDataToSer_test(SendBuf,len,2);
    return;
}



//向显示屏发送心跳包
void HeartBeatToMonitor(byte MsgID)
{
   BWZJHeart    tempBW;
   time_t         nowT;
   struct tm        *t;
   t=localtime(&nowT);
   tempBW.MsgID = MsgID;
   tempBW.Year  = t->tm_year+1900;
   tempBW.Month = t->tm_mon+1;
   tempBW.Day   = t->tm_mday;
   tempBW.Hour  = t->tm_hour;
   tempBW.Min   = t->tm_min;
   tempBW.Sec   = t->tm_sec;
   tempBW.crc   = AT1_checksc(tempBW.Length-1, (const unsigned char*)&tempBW);
   sendto(UServerSoc,(char*)&tempBW,tempBW.Length,0,(struct sockaddr *)&ClientAddr1,sizeof(ClientAddr1));
   sendto(UServerSoc,(char*)&tempBW,tempBW.Length,0,(struct sockaddr *)&ClientAddr2,sizeof(ClientAddr2));
   return;
}

void * Thread_Timer(void *lpPara)
{
    uint32_t sec = 0;
    unsigned int t_index=0;
    int opType=0;
    byte msgid =0;
    while (1)
    {
        usleep(1000*200);
        sec++;
        opType=GetOpType();//检查是否有动作发生   
        if (opType>0 || sec%10 == 0) //每2秒或有事件发生时向服务器发送车载数据
        {
            //ServerMSGSend();//向服务器发送车载数据
            ServerMSGSend_test(t_index++);//向服务器发送车载数据
        }
        else if(sec%25 == 0){
         /* HeartBeatToMonitor(msgid++);//向显示屏发送心跳包
            Global_Var.preStationIndex = Global_Var.curStationIndex;
            Global_Var.curStationIndex = CheckifinStation(GPSData.longitude,GPSData.latitude);//确定机车是否在站场内
            if( Global_Var.curStationIndex !=-1){
                Global_Var.TLJID=Global_Var.StationS[Global_Var.curStationIndex].TLJ;
                strcpy(Global_Var.TLJName,Global_Var.StationS[Global_Var.curStationIndex].TLJ_Name);
                Global_Var.StationID=Global_Var.StationS[Global_Var.curStationIndex].StationID;
                strcpy(Global_Var.stationName,Global_Var.StationS[Global_Var.curStationIndex].Station_Name);
            }
            if(Global_Var.preStationIndex ==-1 && Global_Var.curStationIndex!=-1){
                char logbuf[512];
                sprintf(logbuf,"进入%站",Global_Var.stationName);
                rk_syslog(logbuf);
            }
            if(Global_Var.preStationIndex !=-1 && Global_Var.curStationIndex==-1){
                char logbuf[512];
                sprintf(logbuf,"离开%站",Global_Var.StationS[Global_Var.preStationIndex].Station_Name);
                rk_syslog(logbuf);
            }
        */
        }
    }
}
//处理北斗差分信息包
void ProcessMSG82Info(int comfd,unsigned char *RecvB, int len)
{
   write(comfd,&RecvB[1],len-1);
}

//处理调车进路信息包
void ProcessMSG83Info(unsigned char *RecvB, int len)
{
  DCHERouteInfoHead *tempHead;
  tempHead=(DCHERouteInfoHead *)RecvB;
  int pos = sizeof(DCHERouteInfoHead);
  char dateBuf[100];
  sprintf(dateBuf,"%4d/%2d/%2d %2d:%2d:%2d",tempHead->Year-1900,tempHead->Month-1,tempHead->Day,
                                               tempHead->Hour,tempHead->Min,tempHead->Sec);
  struct tm servTm;
  strptime(dateBuf,"%Y/%m/%d %H:%M:%S",&servTm); // 将"YYYY-MM-DD hh:mm:ss" 转换为tm
  time_t serveT = mktime(&servTm); // 将服务器时间转换为1970年以来的秒
  time_t nowT ;      // time_t就是long int 类型
  nowT = time(0);    // 取得当前时间 ,秒级
  if((nowT-serveT)>120) return;
  if( tempHead->TLJ!=Global_Var.TLJID || tempHead->StationID != Global_Var.StationID) return;

  struct tm *t;
  t=localtime(&nowT);
  char sentbuf[10240];
  int  slen=0;
  sentbuf[0]=0x0B;
  sentbuf[3]=1;
  uint16_t tyear=t->tm_year+1900;
  sentbuf[4] = tyear;
  sentbuf[5] = tyear>>8;
  sentbuf[6] = t->tm_mon+1;
  sentbuf[7] = t->tm_mday;
  sentbuf[8] = t->tm_hour;
  sentbuf[9] = t->tm_min;
  sentbuf[10] = t->tm_sec;
  sentbuf[11] = tempHead->TLJ;
  sentbuf[12] = tempHead->StationID;
  sentbuf[13] = tempHead->StationID >>8;
  for(int i=pos;i<len;i++){
     sentbuf[14+i-pos]=RecvB[i];
  }
  slen=15+len-pos;
  sentbuf[1] = slen;
  sentbuf[2] = slen>>8;
  sentbuf[slen-1]=0;
  for(int i=0;i<slen-1;i++){
    sentbuf[slen-1]=sentbuf[slen-1]+sentbuf[i];
  }
  sendto(UServerSoc,(char*)sentbuf,slen,0,(struct sockaddr *)&ClientAddr1,sizeof(ClientAddr1));
  sendto(UServerSoc,(char*)sentbuf,slen,0,(struct sockaddr *)&ClientAddr2,sizeof(ClientAddr2));
}


//处理调车防护信息包
void ProcessMSG84Info(unsigned char *RecvB, int len)
{
   DCHEGouardInfoHead *tempHead;
   tempHead=(DCHEGouardInfoHead *)RecvB;
   Global_Var.TimeStamp = tempHead->MSGID;
   Global_Var.TLJID = tempHead->TLJ;
   Global_Var.StationID = tempHead->StationID;
   int  pos = sizeof(DCHEGouardInfoHead);
   if(RecvB[pos]==0){ ////防控项点
       pos=pos+5;
       byte NumA = RecvB[pos];
       pos = pos +1;
       Global_Var.KeyPoint_N = NumA;
       Global_Var.KeyPointS.clear();
       FKPoint tempPoint;
       for(int i=0;i<NumA;i++){
           tempPoint.PointType = RecvB[pos];
           pos = pos +1;
           tempPoint.PointID = *(uint16_t *)&RecvB[pos];
           pos = pos +2;
           for (int j = 0; j < 12; j++) {
              tempPoint.PointName[j] = RecvB[pos + j];
           }
           pos = pos +12;
           tempPoint.PointV = *(uint32_t *)&RecvB[pos];
           pos = pos +4;
           tempPoint.PointDis = *(uint32_t *)&RecvB[pos];
           pos = pos +6;
           Global_Var.KeyPointS.push_back(tempPoint);
       }
   }

   if(RecvB[pos]==1){ //股道
       pos=pos+5;
       byte NumB = RecvB[pos];
       pos = pos +1;
       Global_Var.KeyLine_N=NumB;
       Global_Var.KeyLineS.clear();
       Railway tempRail;
       for(int i =0; i<NumB;i++){
          tempRail.RailwayID = *(uint16_t *)&RecvB[pos];
          pos = pos +6;
          for (int j = 0; j < 30; j++) {
             tempRail.RailwayName[j] = RecvB[pos + j];
          }
          pos = pos +30;
          tempRail.DisToDC =  *(uint32_t *)&RecvB[pos];
          pos = pos +4;
          tempRail.RailLength =  *(uint32_t *)&RecvB[pos];
          pos = pos +4;
          tempRail.RailwayStat =   *(uint16_t *)&RecvB[pos];
          pos = pos +2;
          tempRail.MaxSpeed =   *(uint16_t *)&RecvB[pos];
          pos = pos +2;
          tempRail.direct =RecvB[pos];
          pos=pos+1;
          tempRail.xb =tempHead->xb;
          Global_Var.KeyLineS.push_back(tempRail);
       }
   }
}

//处理机车位置信息包
void ProcessMSG85Info(unsigned char *RecvB, int len)
{
     TRAINPOSInfo *tempObj;
     tempObj = (TRAINPOSInfo *)RecvB;
     Global_Var.TimeStamp = tempObj->MSGID;
     if( (Global_Var.TLJID != tempObj->TLJ) ||
         (Global_Var.StationID != tempObj->StationID)){ //进入新站场
          Global_Var.TLJID = tempObj->TLJ;
          Global_Var.StationID = tempObj->StationID;
     }
}

//接收服务器数据线程
void* Thread_RecvFromServer(void *lpPara)
{
   int testz;
   unsigned char RecBuf[10*1024] = "";
   int comfd=*(int *)lpPara;  
   while(1)
   {
       int res = recv(UServerSoc,(char*)RecBuf,sizeof(RecBuf),0);
       //if(res>0) printf("Receive %d,  Frameid:%d\n",res,(unsigned char)RecBuf[0]);
       //continue;
       if (res <= 0) continue;
       if(RecBuf[0] != 0x10 || RecBuf[1] != 0x02) continue;

       unsigned char DataBuf[5*1024] = "";
       DataDLEDealFun(DataBuf,RecBuf,res,1);

       uint32_t len = DataBuf[2] << 8 | DataBuf[3];
       uint16_t SCrc = DataBuf[len + 2] << 8 | DataBuf[len + 3];
       uint16_t Crc = GetCRC16((char*)(DataBuf+2),len);
       if(Crc != SCrc)  continue;           //校验失败 跳过
       if (RecBuf[18] != 0xFF)  continue;   //非业务数据 跳过
       if(DataBuf[19] == 1)//处理zlib压缩数据
       {
           unsigned char UzDataBuf[5*1024] = "";
           unsigned long UzLen = sizeof(UzDataBuf);
           uncompress(UzDataBuf,&UzLen,&DataBuf[6+19],len - 17 - 6); //解压缩
           int OrLen = DataBuf[4+19]|DataBuf[5+19]<<8;
           if (OrLen != UzLen)  continue;  //解压缩失败 跳过
           if (UzDataBuf[0] == 0x5A && UzDataBuf[1] == 0x5A)//业务数据
           {
               int pos = 8;
               while(pos < OrLen - 4)//分解数据
               {
                   int pLen = UzDataBuf[pos + 1] | UzDataBuf[pos + 2]<<8;
                   if (pLen  == 0)    break;
                   switch (UzDataBuf[pos])
                   {
                       case 0x82: //北斗差分信息包
                           ProcessMSG82Info(comfd,&UzDataBuf[pos],pLen);
                           break;
                       case 0x83://调车进路信息包
                           ProcessMSG83Info(&UzDataBuf[pos],pLen);
                           break;
                       case 0x84://调车防护信息包
                           sMSGId = UzDataBuf[pos+7] | UzDataBuf[pos+8] << 8 | UzDataBuf[pos+9] << 16 | UzDataBuf[pos+10] << 24;
                           ProcessMSG84Info(&UzDataBuf[pos],pLen);
                           break;
                       case 0x85://机车位置信息包
                           ProcessMSG85Info(&UzDataBuf[pos], pLen);
                           break;
                       case  0x88:
                       case  0x89:
                           testz=MapDatQueue.size();
                           MapDataBuf tempbuf;
                           tempbuf.len = pLen;
                           memcpy(tempbuf.buf,&UzDataBuf[pos],pLen);
                           mtxMapDatQueue.lock();
                           MapDatQueue.push(tempbuf);
                           mtxMapDatQueue.unlock();
                           testz=MapDatQueue.size();
                           break;
                       default:
                           break;
                   }
                   pos += pLen;
              }

           }//业务数据

       }//处理zlib压缩数据结束
       else if(DataBuf[19] == 0 && DataBuf[25]==0x5A && DataBuf[26]==0x5A)//处理非zlib压缩数据
       {
           int pos=25;
            pos=pos+2;
           int OrLen = DataBuf[23]|DataBuf[24]<<8;
           uint32_t crc = CRC32(&DataBuf[25],OrLen-4);
           uint32_t ocrc = DataBuf[OrLen-4+25] | DataBuf[OrLen-3+25] << 8 | DataBuf[OrLen-2+25] << 16 |DataBuf[OrLen-1+25] <<24;
           pos=pos+2;
           int ver= DataBuf[pos+1]|DataBuf[pos+2]<<8; 
           pos=pos+2;
           int f_index=DataBuf[pos+1]|DataBuf[pos+2]<<8; 
           pos=pos+2;
           while(pos <OrLen+25-4)//分解数据
           {
              int pLen = DataBuf[pos + 1] | DataBuf[pos + 2]<<8;
              if (pLen == 0)  break;
              if(DataBuf[pos]==0x88 || DataBuf[pos]==0x89){
                    MapDataBuf tempbuf;
                    tempbuf.len=pLen;
                    memcpy(tempbuf.buf,&DataBuf[pos],pLen);
                    mtxMapDatQueue.lock();
                    MapDatQueue.push(tempbuf);
                    mtxMapDatQueue.unlock();
              }
              pos += pLen;
           }

       }//处理非zlib压缩数据结束

   }//while end

}

//发送A信息给显示屏
void SendAInfotoMonitor(byte *buf,int len)
{
  byte sentbuf[1024]={0};
  DCMonitorFHInfoHead tempHead;
  tempHead.BDStat = Global_Var.BDS_Stat;
  tempHead.TAXStat = Global_Var.TAX_Stat;
  tempHead.NetStat = Global_Var.Net_Stat;
  tempHead.TLJ = Global_Var.TLJID;
  tempHead.StationID = Global_Var.StationID;
  tempHead.xb = Global_Var.train_xb;
  tempHead.Speed = Global_Var.Speed;
  tempHead.DatStat =1;
  tempHead.Length=sizeof(DCMonitorFHInfoHead)+len+16;//21-5
  struct tm *t;
  time_t tt;
  time(&tt);
  t=localtime(&tt);
  tempHead.Year = t->tm_year+1900;
  tempHead.Month =t->tm_mon+1;
  tempHead.Day = t->tm_mday;
  tempHead.Hour = t->tm_hour;
  tempHead.Min = t->tm_min;
  tempHead.Sec = t->tm_sec;
  tempHead.DCStat =curTaxData.stateDLoco;
  int headlen=sizeof(DCMonitorFHInfoHead);
  byte *temp;
  temp=(byte *)&tempHead;

  for(int i =0;i<headlen;i++){
      sentbuf[i]=temp[i];
  }

  for(int i = 0;i<len;i++){
      sentbuf[i+headlen]=buf[i];
  }

  byte crc =0;
  for(int i=0;i<tempHead.Length+4;i++){
      crc=crc+sentbuf[i];
  }
  sentbuf[tempHead.Length+4]=crc;
  int ss=tempHead.Length+5;
  int zres = sendto(UServerSoc,(char*)sentbuf,tempHead.Length+5,0,(struct sockaddr *)&ClientAddr1,sizeof(ClientAddr1));
  sendto(UServerSoc,(char*)sentbuf,tempHead.Length+5,0,(struct sockaddr *)&ClientAddr2,sizeof(ClientAddr2));

}

//防控项点控车
int ProcessPointControl(FKPoint tPoint)
{
  int stat=0;
  if(tPoint.PointType !=4)
       return stat;
  float DistoP = tPoint.PointDis;
  //LKJ  15km/h 控车
  if (DistoP <= 12800 && DistoP > 4800 && !preSpeed15Shutdown)             //第一次控制车速
  {
       GPIOPulse(200000);
       first_stop=what_time_is_it_now();
       preSpeed15Shutdown = 1;
       char logbuf[1024];
       sprintf(logbuf,"第一次15km/h控速, %s, 距离:%4.1f",tPoint.PointName
                                                      ,tPoint.PointDis/100.0);
       rk_syslog(logbuf);
       stat=1;

  }else if (DistoP <= 12800 && DistoP > 2800 && preSpeed15Shutdown && curTaxData.Speed >15){
       int tempsec = what_time_is_it_now()-first_stop;
       if(tempsec>7000 && !preSpeed15Shutdown_7s){//超过7秒后速度没降下来停车
           GPIOPulse(200000);
           preSpeed15Shutdown_7s = 1;
           char logbuf[1024];
           sprintf(logbuf,"7秒后第二次控停, %s, 距离:%4.1f",tPoint.PointName
                                                        ,tPoint.PointDis/100.0);
           rk_syslog(logbuf);
           stat=2;

        }
  }
  //LKJ控车  停车
  if (curTaxData.Speed > 0         //第一次控停
              && DistoP <= 3100 && DistoP > 0
              && preShutdown < 0)
  {
      GPIOPulse(200000);
      preShutdown = DistoP;
      char logbuf[1024];
      sprintf(logbuf,"第一次控停, %s, 距离:%4.1f",tPoint.PointName
                                              ,tPoint.PointDis/100.0);
      rk_syslog(logbuf);
      stat=3;
  }
  else if (curTaxData.Speed > 0  //第二次控停
          &&  DistoP < 3100 && DistoP > 0
          && preShutdown > 0
          && (preShutdown - DistoP) > 3
          && !preShutdown_3m)
  {
      GPIOPulse(200000);
      preShutdown = DistoP;
      preShutdown_3m = 1;
      char logbuf[1024];
      sprintf(logbuf,"第二次控停, %s, 距离:%4.1f",tPoint.PointName
                                              ,tPoint.PointDis/100.0);
      rk_syslog(logbuf);
      stat=4;

  }
  return stat;
}

//车载主机计算距离
void* Thread_CalDis(void *lpPara)
{

    struct sockaddr_in ClientAddr1;
    memset(&ClientAddr1, 0, sizeof(ClientAddr1));
    ClientAddr1.sin_family = AF_INET;
    ClientAddr1.sin_port = htons(10001);
    ClientAddr1.sin_addr.s_addr = inet_addr(ClientAIP);

    struct sockaddr_in ClientAddr2;
    memset(&ClientAddr2, 0, sizeof(ClientAddr2));
    ClientAddr2.sin_family = AF_INET;
    ClientAddr2.sin_port = htons(10001);
    ClientAddr2.sin_addr.s_addr = inet_addr(ClientBIP);
    byte logindex=0;
    while(1){
      if(Global_Var.KeyLineS.size()==0) continue;
      long timenow=what_time_is_it_now();
      float f_interval=(timenow-Global_Var.TimeStamp)/1000.0;
      uint32_t dis=0;
      int CurLineID;
      //服务器信号滞后四个周期以内，车载主机计算
      if(f_interval<4 && Global_Var.StationID !=0){
         logindex=0;
         int pointFirst=-1;
         //dis=curTaxData.Speed*f_interval*27.78;
         dis=curTaxData.Speed*f_interval*27.78;
         Global_Var.dis=dis;
         std::vector<FKPoint> curPoints;
         for(int i =0;i<Global_Var.KeyPoint_N;i++){
             FKPoint tempPoint;
             tempPoint=Global_Var.KeyPointS[i];
             int temp_ds=Global_Var.KeyPointS[i].PointDis-dis;
             if(temp_ds>=0){//确定前方防控点距离
                //确定前方防控点距离
                tempPoint.PointDis=temp_ds;
                curPoints.push_back(tempPoint);
                if(pointFirst==-1) pointFirst=i;//记录前方第1个防控项点
             }
         }
         //如需控车，检查是否需要对当前防控项点进行控车，目前只针对土挡
         if(pointFirst!=-1 && CtrlStat && curTaxData.stateDLoco
                 && TaxStat && (curPoints[0].PointType ==4 ||curPoints[0].PointType ==5))
         {
                int opty=  ProcessPointControl(curPoints[0]);
                if(opty>0) PushOpType(opty);
         }
         //控车结束
         //初始化控车状态变量，以便于下一次控车
         if(pointFirst==-1 
                 || Global_Var.KeyPointS[pointFirst].PointDis > 12800
                 || Global_Var.KeyPointS[pointFirst].PointDis < 3100  )
         {
             preSpeed15Shutdown = 0;
             preSpeed15Shutdown_7s = 0;
         }
         if (pointFirst !=-1 && curPoints[0].PointDis > 3100) {
             preShutdown = -1;
             preShutdown_3m = 0;
         }

         byte bbuf[2048]={0};
         int  tlen=0;
         int  tpn=curPoints.size();//前方进路防护项点数量
         bbuf[tlen++]=tpn;
         for(int i=0;i<tpn;i++)
         {
            bbuf[tlen++] = curPoints[0].PointType;          //防护项点类型
            bbuf[tlen++] = curPoints[0].PointID ;           //防护项点序号
            bbuf[tlen++] = curPoints[0].PointID >>8 ;
             for( int j=0;j<12;j++){                         //防护项点名称
               bbuf[tlen+j]= curPoints[0].PointName[j];
            }
            tlen=tlen+12;   
            bbuf[tlen++]=curPoints[0].PointV;               //防护项点状态
            bbuf[tlen++]=curPoints[0].PointV >> 8;
            bbuf[tlen++]=curPoints[0].PointV >> 16;
            bbuf[tlen++]=curPoints[0].PointV >> 24;
            bbuf[tlen++]=curPoints[0].PointDis;             //防护项点距离
            bbuf[tlen++]=curPoints[0].PointDis >> 8;
            bbuf[tlen++]=curPoints[0].PointDis >> 16;
            bbuf[tlen++]=curPoints[0].PointDis >> 24;                                
         }


         float curDisToDC=0;
         float map_pos;
         CurLineID=-1;
         int temp_ds;
         if(Global_Var.KeyLineS[0].direct!=0)
           temp_ds=Global_Var.KeyLineS[0].DisToDC-dis;
         else
           temp_ds=Global_Var.KeyLineS[0].DisToDC;  
         
         if(temp_ds>=0){
                CurLineID=0;
                curDisToDC=temp_ds;
         }else{
             uint32_t tempdis=Global_Var.KeyLineS[0].DisToDC;
             for(int i=1;i< Global_Var.KeyLine_N;i++){
                 tempdis=tempdis+Global_Var.KeyLineS[i].RailLength;
                 temp_ds =tempdis-dis;
                 if(temp_ds>0){
                     CurLineID=i;
                     curDisToDC=temp_ds;
                     break;
                 }
             }
             if(tempdis==Global_Var.KeyLineS[0].DisToDC) CurLineID=-1;
         }
         
         if(CurLineID==-1) continue;//计算超出了进路范围，跳出以下部分

         bbuf[tlen++]=Global_Var.KeyLineS[CurLineID].RailwayID;         //机车所在股道序号
         bbuf[tlen++]=Global_Var.KeyLineS[CurLineID].RailwayID>>8;
         for(int j=0;j<30;j++){                                         //机车所在股道名称
            bbuf[tlen+j]= Global_Var.KeyLineS[CurLineID].RailwayName[j];
         }
         tlen=tlen+30;
         if(Global_Var.KeyLineS[CurLineID].direct==1){
              map_pos=curDisToDC/Global_Var.KeyLineS[CurLineID].RailLength;
              map_pos=1-map_pos;
         }else{
             map_pos=curDisToDC/Global_Var.KeyLineS[CurLineID].RailLength;
         }

         byte * pfloat;
         pfloat=(byte *)&map_pos;
         for(int i=0;i<4;i++){           //机车在股道上的显示位置
            bbuf[tlen++]=pfloat[i];
         }
         int railN=Global_Var.KeyLine_N-CurLineID-1;
         bbuf[tlen++]=railN;//机车前方进路股道数量
         for(int i=CurLineID+1;i<Global_Var.KeyLine_N;i++){
             bbuf[tlen++]=Global_Var.KeyLineS[i].RailwayID;
             bbuf[tlen++]=Global_Var.KeyLineS[i].RailwayID>>8;                //前方股道序号
             for(int j=0;j<30;j++){
                 bbuf[tlen+j]=Global_Var.KeyLineS[i].RailwayName[j];          //前方股道名称
             }
             tlen=tlen+30;
         }

        //SendAInfotoMonitor(bbuf,tlen);

        // //测试用///////////////////////////////////
         TrainSendInfo TrainInfo;
         TrainInfo.header = 0x55BB;
         TrainInfo.length=sizeof(TrainSendInfo);
         TrainInfo.StationID = Global_Var.StationID;
         TrainInfo.direction=Global_Var.KeyLineS[CurLineID].direct;
         TrainInfo.LinePer =map_pos;
         TrainInfo.Speed =  curTaxData.Speed;
         TrainInfo.MaxSpeed = curTaxData.MaxSpeed;
         struct tm *t;
         time_t tt;
         time(&tt);
         t=localtime(&tt);
         TrainInfo.TAXTime.Year = t->tm_year+1900;
         TrainInfo.TAXTime.Month = t->tm_mon+1;
         TrainInfo.TAXTime.Day = t->tm_mday;
         TrainInfo.TAXTime.Hour = t->tm_hour;
         TrainInfo.TAXTime.Minute = t->tm_min;
         TrainInfo.TAXTime.Second = t->tm_sec;
         TrainInfo.TAXStat=1;
         TrainInfo.GPSStat=4;
         strcpy(TrainInfo.LineName,Global_Var.KeyLineS[CurLineID].RailwayName);
         if(curPoints.size()>0){
            TrainInfo.PointType = curPoints[0].PointType;
            TrainInfo.DistoP    = curPoints[0].PointDis/100.0;
            strcpy(TrainInfo.LampName,curPoints[0].PointName);
         }else if(curPoints.size()==0){
            TrainInfo.PointType=6;
            TrainInfo.DistoP=Global_Var.KeyLineS[CurLineID].DisToDC/100;
         } 
         printf("%s,  %f,  %d,  %d,  %d, %3.1f\n",TrainInfo.LineName,TrainInfo.LinePer,
                          Global_Var.KeyLineS[CurLineID].DisToDC,dis,
                          Global_Var.KeyLineS[CurLineID].RailLength,f_interval);      
         sendto(UServerSoc,(char*)&TrainInfo,sizeof(TrainInfo),0,(struct sockaddr *)&ClientAddr2,sizeof(ClientAddr1));  
        //保存数据
        ////////////////////////////////////////////////
         RecordFileData bw;
         GetInfoFromGlobal(&bw);
         bw.DateTime[0] = t->tm_year-100;
         bw.DateTime[1] = t->tm_mon+1;
         bw.DateTime[2] = t->tm_mday;
         bw.DateTime[3] = t->tm_hour;
         bw.DateTime[4] = t->tm_min;
         bw.DateTime[5] = t->tm_sec;
         if(curPoints.size()>0){
            bw.first_p     = curPoints[0];
            bw.end_p       = curPoints[curPoints.size()-1];
         }
         bw.linedirect  = Global_Var.KeyLineS[CurLineID].direct;
         bw.LinePer     = map_pos;
         strcpy(bw.LineName,Global_Var.KeyLineS[CurLineID].RailwayName);
         Push_RecordData(bw);
        ////////////////////////////////////////////////
      }

      if(f_interval>12 && Global_Var.StationID !=0 && logindex==0){
          logindex++;
          rk_syslog("服务器通信故障");
      }
      usleep(200*1000);
    }
}


//接收显示屏UPD数据
int UdpFromMonitorInit()
{
    int Soc = 0;//用于接收显示屏UPD数据的Soc
    if ((Soc = socket(AF_INET, SOCK_DGRAM, 0)) < 0)
    {
        printf("UdpfromMonitor Init socket error!\n");
        return -1;
    }

    struct sockaddr_in LocAddr;
    memset(&LocAddr, 0, sizeof(LocAddr));
    LocAddr.sin_family = AF_INET;
    LocAddr.sin_port = htons(10003);
    LocAddr.sin_addr.s_addr = inet_addr("192.168.1.186");
    //LocAddr.sin_addr.s_addr = INADDR_ANY;
    if(bind(Soc, (struct sockaddr *)&LocAddr, sizeof(sockaddr)) < 0)
        return -1;
    return Soc;
}

//接收显示屏UPD数据
void* Thread_GetUDPofMonitor(void *lpPara)
{
  int SocUDPfrMon = UdpFromMonitorInit();//用于接收显示屏UPD数据的Soc
  byte RecBuf[512] = "";
  while(1)
  {
      int res = recv(SocUDPfrMon,RecBuf,sizeof(RecBuf),0);
      if(res <0) continue;
      if (RecBuf[0] == 0xFF && RecBuf[1] == 0x77){//心跳包
          if (RecBuf[5] == 'A'){
               MonA_Stat++;
          }else if(RecBuf[5] == 'B'){
               MonB_Stat++;
          }
      }
      usleep(10000);
  }

}


//显示屏地址初始化



void* Thread_BeatTest(void *lpPara)
{
    memset(&ClientAddr1, 0, sizeof(ClientAddr1));
    ClientAddr1.sin_family = AF_INET;
    ClientAddr1.sin_port = htons(10001);
    ClientAddr1.sin_addr.s_addr = inet_addr("192.168.60.71");
    memset(&ClientAddr2, 0, sizeof(ClientAddr2));
    ClientAddr2.sin_family = AF_INET;
    ClientAddr2.sin_port = htons(10001);
    ClientAddr2.sin_addr.s_addr = inet_addr("192.168.60.72");
    byte msgID=0;
    while(1){
        BWZJHeart    tempBW;
        struct tm *t;
        time_t tt;
        time(&tt);
        t=localtime(&tt);

        tempBW.MsgID = msgID++;
        tempBW.BDStat =3;
        tempBW.TaxStat =1;
        tempBW.BDStat = 4;
        tempBW.Db_v   = 5;
        tempBW.Year  = t->tm_year+1900;
        tempBW.Month = t->tm_mon+1;
        tempBW.Day   = t->tm_mday;
        tempBW.Hour  = t->tm_hour;
        tempBW.Min   = t->tm_min;
        tempBW.Sec   = t->tm_sec;
        tempBW.crc   = AT1_checksc(sizeof(tempBW)-1, (const unsigned char*)&tempBW);
        sendto(UServerSoc,(char*)&tempBW,sizeof(tempBW),0,(struct sockaddr *)&ClientAddr1,sizeof(ClientAddr1));
        sleep(5);
    }
}
