[{"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/main.cpp.o -c /home/<USER>/code/DS_ACServer/main.cpp", "file": "/home/<USER>/code/DS_ACServer/main.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/gps.cpp.o -c /home/<USER>/code/DS_ACServer/gps.cpp", "file": "/home/<USER>/code/DS_ACServer/gps.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/crc.cpp.o -c /home/<USER>/code/DS_ACServer/crc.cpp", "file": "/home/<USER>/code/DS_ACServer/crc.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/md5.cpp.o -c /home/<USER>/code/DS_ACServer/md5.cpp", "file": "/home/<USER>/code/DS_ACServer/md5.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/sm4.cpp.o -c /home/<USER>/code/DS_ACServer/sm4.cpp", "file": "/home/<USER>/code/DS_ACServer/sm4.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/gpio.cpp.o -c /home/<USER>/code/DS_ACServer/gpio.cpp", "file": "/home/<USER>/code/DS_ACServer/gpio.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/wdog.cpp.o -c /home/<USER>/code/DS_ACServer/wdog.cpp", "file": "/home/<USER>/code/DS_ACServer/wdog.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/n_tax.cpp.o -c /home/<USER>/code/DS_ACServer/n_tax.cpp", "file": "/home/<USER>/code/DS_ACServer/n_tax.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/ntrip.cpp.o -c /home/<USER>/code/DS_ACServer/ntrip.cpp", "file": "/home/<USER>/code/DS_ACServer/ntrip.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/ds_lsp.cpp.o -c /home/<USER>/code/DS_ACServer/ds_lsp.cpp", "file": "/home/<USER>/code/DS_ACServer/ds_lsp.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/readcfg.cpp.o -c /home/<USER>/code/DS_ACServer/readcfg.cpp", "file": "/home/<USER>/code/DS_ACServer/readcfg.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/checkip.cpp.o -c /home/<USER>/code/DS_ACServer/checkip.cpp", "file": "/home/<USER>/code/DS_ACServer/checkip.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/tinyxml2.cpp.o -c /home/<USER>/code/DS_ACServer/tinyxml2.cpp", "file": "/home/<USER>/code/DS_ACServer/tinyxml2.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/rk_filelog.cpp.o -c /home/<USER>/code/DS_ACServer/rk_filelog.cpp", "file": "/home/<USER>/code/DS_ACServer/rk_filelog.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/map_update.cpp.o -c /home/<USER>/code/DS_ACServer/map_update.cpp", "file": "/home/<USER>/code/DS_ACServer/map_update.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/test_dat.cpp.o -c /home/<USER>/code/DS_ACServer/test_dat.cpp", "file": "/home/<USER>/code/DS_ACServer/test_dat.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/save_data.cpp.o -c /home/<USER>/code/DS_ACServer/save_data.cpp", "file": "/home/<USER>/code/DS_ACServer/save_data.cpp"}, {"directory": "/home/<USER>/code/DS_ACServer/build", "command": "/usr/bin/aarch64-linux-gnu-g++   -I/home/<USER>/code/DS_ACServer/inc   -std=c++11 -g   -o CMakeFiles/ACServer.dir/udp_to_server.cpp.o -c /home/<USER>/code/DS_ACServer/udp_to_server.cpp", "file": "/home/<USER>/code/DS_ACServer/udp_to_server.cpp"}]