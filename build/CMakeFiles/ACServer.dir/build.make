# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/DS_ACServer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/DS_ACServer/build

# Include any dependencies generated for this target.
include CMakeFiles/ACServer.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/ACServer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/ACServer.dir/flags.make

CMakeFiles/ACServer.dir/main.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/main.cpp.o: ../main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/ACServer.dir/main.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/main.cpp.o -c /home/<USER>/code/DS_ACServer/main.cpp

CMakeFiles/ACServer.dir/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/main.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/main.cpp > CMakeFiles/ACServer.dir/main.cpp.i

CMakeFiles/ACServer.dir/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/main.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/main.cpp -o CMakeFiles/ACServer.dir/main.cpp.s

CMakeFiles/ACServer.dir/gps.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/gps.cpp.o: ../gps.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/ACServer.dir/gps.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/gps.cpp.o -c /home/<USER>/code/DS_ACServer/gps.cpp

CMakeFiles/ACServer.dir/gps.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/gps.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/gps.cpp > CMakeFiles/ACServer.dir/gps.cpp.i

CMakeFiles/ACServer.dir/gps.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/gps.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/gps.cpp -o CMakeFiles/ACServer.dir/gps.cpp.s

CMakeFiles/ACServer.dir/crc.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/crc.cpp.o: ../crc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/ACServer.dir/crc.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/crc.cpp.o -c /home/<USER>/code/DS_ACServer/crc.cpp

CMakeFiles/ACServer.dir/crc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/crc.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/crc.cpp > CMakeFiles/ACServer.dir/crc.cpp.i

CMakeFiles/ACServer.dir/crc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/crc.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/crc.cpp -o CMakeFiles/ACServer.dir/crc.cpp.s

CMakeFiles/ACServer.dir/md5.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/md5.cpp.o: ../md5.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/ACServer.dir/md5.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/md5.cpp.o -c /home/<USER>/code/DS_ACServer/md5.cpp

CMakeFiles/ACServer.dir/md5.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/md5.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/md5.cpp > CMakeFiles/ACServer.dir/md5.cpp.i

CMakeFiles/ACServer.dir/md5.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/md5.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/md5.cpp -o CMakeFiles/ACServer.dir/md5.cpp.s

CMakeFiles/ACServer.dir/sm4.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/sm4.cpp.o: ../sm4.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/ACServer.dir/sm4.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/sm4.cpp.o -c /home/<USER>/code/DS_ACServer/sm4.cpp

CMakeFiles/ACServer.dir/sm4.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/sm4.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/sm4.cpp > CMakeFiles/ACServer.dir/sm4.cpp.i

CMakeFiles/ACServer.dir/sm4.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/sm4.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/sm4.cpp -o CMakeFiles/ACServer.dir/sm4.cpp.s

CMakeFiles/ACServer.dir/gpio.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/gpio.cpp.o: ../gpio.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/ACServer.dir/gpio.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/gpio.cpp.o -c /home/<USER>/code/DS_ACServer/gpio.cpp

CMakeFiles/ACServer.dir/gpio.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/gpio.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/gpio.cpp > CMakeFiles/ACServer.dir/gpio.cpp.i

CMakeFiles/ACServer.dir/gpio.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/gpio.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/gpio.cpp -o CMakeFiles/ACServer.dir/gpio.cpp.s

CMakeFiles/ACServer.dir/wdog.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/wdog.cpp.o: ../wdog.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/ACServer.dir/wdog.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/wdog.cpp.o -c /home/<USER>/code/DS_ACServer/wdog.cpp

CMakeFiles/ACServer.dir/wdog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/wdog.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/wdog.cpp > CMakeFiles/ACServer.dir/wdog.cpp.i

CMakeFiles/ACServer.dir/wdog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/wdog.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/wdog.cpp -o CMakeFiles/ACServer.dir/wdog.cpp.s

CMakeFiles/ACServer.dir/n_tax.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/n_tax.cpp.o: ../n_tax.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/ACServer.dir/n_tax.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/n_tax.cpp.o -c /home/<USER>/code/DS_ACServer/n_tax.cpp

CMakeFiles/ACServer.dir/n_tax.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/n_tax.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/n_tax.cpp > CMakeFiles/ACServer.dir/n_tax.cpp.i

CMakeFiles/ACServer.dir/n_tax.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/n_tax.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/n_tax.cpp -o CMakeFiles/ACServer.dir/n_tax.cpp.s

CMakeFiles/ACServer.dir/ntrip.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/ntrip.cpp.o: ../ntrip.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/ACServer.dir/ntrip.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/ntrip.cpp.o -c /home/<USER>/code/DS_ACServer/ntrip.cpp

CMakeFiles/ACServer.dir/ntrip.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/ntrip.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/ntrip.cpp > CMakeFiles/ACServer.dir/ntrip.cpp.i

CMakeFiles/ACServer.dir/ntrip.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/ntrip.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/ntrip.cpp -o CMakeFiles/ACServer.dir/ntrip.cpp.s

CMakeFiles/ACServer.dir/ds_lsp.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/ds_lsp.cpp.o: ../ds_lsp.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/ACServer.dir/ds_lsp.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/ds_lsp.cpp.o -c /home/<USER>/code/DS_ACServer/ds_lsp.cpp

CMakeFiles/ACServer.dir/ds_lsp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/ds_lsp.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/ds_lsp.cpp > CMakeFiles/ACServer.dir/ds_lsp.cpp.i

CMakeFiles/ACServer.dir/ds_lsp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/ds_lsp.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/ds_lsp.cpp -o CMakeFiles/ACServer.dir/ds_lsp.cpp.s

CMakeFiles/ACServer.dir/readcfg.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/readcfg.cpp.o: ../readcfg.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/ACServer.dir/readcfg.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/readcfg.cpp.o -c /home/<USER>/code/DS_ACServer/readcfg.cpp

CMakeFiles/ACServer.dir/readcfg.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/readcfg.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/readcfg.cpp > CMakeFiles/ACServer.dir/readcfg.cpp.i

CMakeFiles/ACServer.dir/readcfg.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/readcfg.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/readcfg.cpp -o CMakeFiles/ACServer.dir/readcfg.cpp.s

CMakeFiles/ACServer.dir/checkip.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/checkip.cpp.o: ../checkip.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/ACServer.dir/checkip.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/checkip.cpp.o -c /home/<USER>/code/DS_ACServer/checkip.cpp

CMakeFiles/ACServer.dir/checkip.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/checkip.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/checkip.cpp > CMakeFiles/ACServer.dir/checkip.cpp.i

CMakeFiles/ACServer.dir/checkip.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/checkip.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/checkip.cpp -o CMakeFiles/ACServer.dir/checkip.cpp.s

CMakeFiles/ACServer.dir/tinyxml2.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/tinyxml2.cpp.o: ../tinyxml2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/ACServer.dir/tinyxml2.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/tinyxml2.cpp.o -c /home/<USER>/code/DS_ACServer/tinyxml2.cpp

CMakeFiles/ACServer.dir/tinyxml2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/tinyxml2.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/tinyxml2.cpp > CMakeFiles/ACServer.dir/tinyxml2.cpp.i

CMakeFiles/ACServer.dir/tinyxml2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/tinyxml2.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/tinyxml2.cpp -o CMakeFiles/ACServer.dir/tinyxml2.cpp.s

CMakeFiles/ACServer.dir/rk_filelog.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/rk_filelog.cpp.o: ../rk_filelog.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/ACServer.dir/rk_filelog.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/rk_filelog.cpp.o -c /home/<USER>/code/DS_ACServer/rk_filelog.cpp

CMakeFiles/ACServer.dir/rk_filelog.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/rk_filelog.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/rk_filelog.cpp > CMakeFiles/ACServer.dir/rk_filelog.cpp.i

CMakeFiles/ACServer.dir/rk_filelog.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/rk_filelog.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/rk_filelog.cpp -o CMakeFiles/ACServer.dir/rk_filelog.cpp.s

CMakeFiles/ACServer.dir/map_update.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/map_update.cpp.o: ../map_update.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/ACServer.dir/map_update.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/map_update.cpp.o -c /home/<USER>/code/DS_ACServer/map_update.cpp

CMakeFiles/ACServer.dir/map_update.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/map_update.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/map_update.cpp > CMakeFiles/ACServer.dir/map_update.cpp.i

CMakeFiles/ACServer.dir/map_update.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/map_update.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/map_update.cpp -o CMakeFiles/ACServer.dir/map_update.cpp.s

CMakeFiles/ACServer.dir/test_dat.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/test_dat.cpp.o: ../test_dat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/ACServer.dir/test_dat.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/test_dat.cpp.o -c /home/<USER>/code/DS_ACServer/test_dat.cpp

CMakeFiles/ACServer.dir/test_dat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/test_dat.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/test_dat.cpp > CMakeFiles/ACServer.dir/test_dat.cpp.i

CMakeFiles/ACServer.dir/test_dat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/test_dat.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/test_dat.cpp -o CMakeFiles/ACServer.dir/test_dat.cpp.s

CMakeFiles/ACServer.dir/save_data.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/save_data.cpp.o: ../save_data.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/ACServer.dir/save_data.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/save_data.cpp.o -c /home/<USER>/code/DS_ACServer/save_data.cpp

CMakeFiles/ACServer.dir/save_data.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/save_data.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/save_data.cpp > CMakeFiles/ACServer.dir/save_data.cpp.i

CMakeFiles/ACServer.dir/save_data.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/save_data.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/save_data.cpp -o CMakeFiles/ACServer.dir/save_data.cpp.s

CMakeFiles/ACServer.dir/udp_to_server.cpp.o: CMakeFiles/ACServer.dir/flags.make
CMakeFiles/ACServer.dir/udp_to_server.cpp.o: ../udp_to_server.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/ACServer.dir/udp_to_server.cpp.o"
	/usr/bin/aarch64-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/ACServer.dir/udp_to_server.cpp.o -c /home/<USER>/code/DS_ACServer/udp_to_server.cpp

CMakeFiles/ACServer.dir/udp_to_server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/ACServer.dir/udp_to_server.cpp.i"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/code/DS_ACServer/udp_to_server.cpp > CMakeFiles/ACServer.dir/udp_to_server.cpp.i

CMakeFiles/ACServer.dir/udp_to_server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/ACServer.dir/udp_to_server.cpp.s"
	/usr/bin/aarch64-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/code/DS_ACServer/udp_to_server.cpp -o CMakeFiles/ACServer.dir/udp_to_server.cpp.s

# Object files for target ACServer
ACServer_OBJECTS = \
"CMakeFiles/ACServer.dir/main.cpp.o" \
"CMakeFiles/ACServer.dir/gps.cpp.o" \
"CMakeFiles/ACServer.dir/crc.cpp.o" \
"CMakeFiles/ACServer.dir/md5.cpp.o" \
"CMakeFiles/ACServer.dir/sm4.cpp.o" \
"CMakeFiles/ACServer.dir/gpio.cpp.o" \
"CMakeFiles/ACServer.dir/wdog.cpp.o" \
"CMakeFiles/ACServer.dir/n_tax.cpp.o" \
"CMakeFiles/ACServer.dir/ntrip.cpp.o" \
"CMakeFiles/ACServer.dir/ds_lsp.cpp.o" \
"CMakeFiles/ACServer.dir/readcfg.cpp.o" \
"CMakeFiles/ACServer.dir/checkip.cpp.o" \
"CMakeFiles/ACServer.dir/tinyxml2.cpp.o" \
"CMakeFiles/ACServer.dir/rk_filelog.cpp.o" \
"CMakeFiles/ACServer.dir/map_update.cpp.o" \
"CMakeFiles/ACServer.dir/test_dat.cpp.o" \
"CMakeFiles/ACServer.dir/save_data.cpp.o" \
"CMakeFiles/ACServer.dir/udp_to_server.cpp.o"

# External object files for target ACServer
ACServer_EXTERNAL_OBJECTS =

ACServer: CMakeFiles/ACServer.dir/main.cpp.o
ACServer: CMakeFiles/ACServer.dir/gps.cpp.o
ACServer: CMakeFiles/ACServer.dir/crc.cpp.o
ACServer: CMakeFiles/ACServer.dir/md5.cpp.o
ACServer: CMakeFiles/ACServer.dir/sm4.cpp.o
ACServer: CMakeFiles/ACServer.dir/gpio.cpp.o
ACServer: CMakeFiles/ACServer.dir/wdog.cpp.o
ACServer: CMakeFiles/ACServer.dir/n_tax.cpp.o
ACServer: CMakeFiles/ACServer.dir/ntrip.cpp.o
ACServer: CMakeFiles/ACServer.dir/ds_lsp.cpp.o
ACServer: CMakeFiles/ACServer.dir/readcfg.cpp.o
ACServer: CMakeFiles/ACServer.dir/checkip.cpp.o
ACServer: CMakeFiles/ACServer.dir/tinyxml2.cpp.o
ACServer: CMakeFiles/ACServer.dir/rk_filelog.cpp.o
ACServer: CMakeFiles/ACServer.dir/map_update.cpp.o
ACServer: CMakeFiles/ACServer.dir/test_dat.cpp.o
ACServer: CMakeFiles/ACServer.dir/save_data.cpp.o
ACServer: CMakeFiles/ACServer.dir/udp_to_server.cpp.o
ACServer: CMakeFiles/ACServer.dir/build.make
ACServer: CMakeFiles/ACServer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Linking CXX executable ACServer"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/ACServer.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/ACServer.dir/build: ACServer

.PHONY : CMakeFiles/ACServer.dir/build

CMakeFiles/ACServer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/ACServer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/ACServer.dir/clean

CMakeFiles/ACServer.dir/depend:
	cd /home/<USER>/code/DS_ACServer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/code/DS_ACServer /home/<USER>/code/DS_ACServer /home/<USER>/code/DS_ACServer/build /home/<USER>/code/DS_ACServer/build /home/<USER>/code/DS_ACServer/build/CMakeFiles/ACServer.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/ACServer.dir/depend

