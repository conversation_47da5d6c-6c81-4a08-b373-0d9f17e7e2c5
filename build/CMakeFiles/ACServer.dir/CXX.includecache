#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../inc/zconf.h
stddef.h
-
windows.h
-
limits.h
-
sys/types.h
-
stdarg.h
-
stddef.h
-
unistd.h
-
unixio.h
-

../inc/zlib.h
zconf.h
../inc/zconf.h

/home/<USER>/code/DS_ACServer/DS_Data.h
string.h
-
vector
-
mutex
-

/home/<USER>/code/DS_ACServer/TaxData.h

/home/<USER>/code/DS_ACServer/checkip.h

/home/<USER>/code/DS_ACServer/ds_station.h
vector
-
TaxData.h
/home/<USER>/code/DS_ACServer/TaxData.h

/home/<USER>/code/DS_ACServer/gpio.h
queue
-
mutex
-

/home/<USER>/code/DS_ACServer/gps.h

/home/<USER>/code/DS_ACServer/main.cpp
stdio.h
-
stdlib.h
-
string.h
-
sys/time.h
-
errno.h
-
signal.h
-
fcntl.h
-
unistd.h
-
arpa/inet.h
-
sys/socket.h
-
netinet/in.h
-
termios.h
-
pthread.h
-
TaxData.h
/home/<USER>/code/DS_ACServer/TaxData.h
gps.h
/home/<USER>/code/DS_ACServer/gps.h
ntrip.h
/home/<USER>/code/DS_ACServer/ntrip.h
checkip.h
/home/<USER>/code/DS_ACServer/checkip.h
wdog.h
/home/<USER>/code/DS_ACServer/wdog.h
tx2_filelog.h
/home/<USER>/code/DS_ACServer/tx2_filelog.h
gpio.h
/home/<USER>/code/DS_ACServer/gpio.h
readcfg.h
/home/<USER>/code/DS_ACServer/readcfg.h
udp_to_server.h
/home/<USER>/code/DS_ACServer/udp_to_server.h
DS_Data.h
/home/<USER>/code/DS_ACServer/DS_Data.h
ds_station.h
/home/<USER>/code/DS_ACServer/ds_station.h
save_data.h
/home/<USER>/code/DS_ACServer/save_data.h
map_update.h
/home/<USER>/code/DS_ACServer/map_update.h
zlib.h
/home/<USER>/code/DS_ACServer/zlib.h

/home/<USER>/code/DS_ACServer/map_update.h

/home/<USER>/code/DS_ACServer/ntrip.h

/home/<USER>/code/DS_ACServer/readcfg.h

/home/<USER>/code/DS_ACServer/save_data.h
DS_Data.h
/home/<USER>/code/DS_ACServer/DS_Data.h

/home/<USER>/code/DS_ACServer/tx2_filelog.h

/home/<USER>/code/DS_ACServer/udp_to_server.h

/home/<USER>/code/DS_ACServer/wdog.h

