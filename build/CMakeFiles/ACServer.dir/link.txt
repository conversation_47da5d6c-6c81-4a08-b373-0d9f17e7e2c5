/usr/bin/aarch64-linux-gnu-g++   -std=c++11 -g   CMakeFiles/ACServer.dir/main.cpp.o CMakeFiles/ACServer.dir/gps.cpp.o CMakeFiles/ACServer.dir/crc.cpp.o CMakeFiles/ACServer.dir/md5.cpp.o CMakeFiles/ACServer.dir/sm4.cpp.o CMakeFiles/ACServer.dir/gpio.cpp.o CMakeFiles/ACServer.dir/wdog.cpp.o CMakeFiles/ACServer.dir/n_tax.cpp.o CMakeFiles/ACServer.dir/ntrip.cpp.o CMakeFiles/ACServer.dir/ds_lsp.cpp.o CMakeFiles/ACServer.dir/readcfg.cpp.o CMakeFiles/ACServer.dir/checkip.cpp.o CMakeFiles/ACServer.dir/tinyxml2.cpp.o CMakeFiles/ACServer.dir/rk_filelog.cpp.o CMakeFiles/ACServer.dir/map_update.cpp.o CMakeFiles/ACServer.dir/test_dat.cpp.o CMakeFiles/ACServer.dir/save_data.cpp.o CMakeFiles/ACServer.dir/udp_to_server.cpp.o  -o ACServer  -L/home/<USER>/code/DS_ACServer/lib -Wl,-rpath,/home/<USER>/code/DS_ACServer/lib -lm -ldl -lpthread -lz 
