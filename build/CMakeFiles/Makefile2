# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# The main recursive all target
all:

.PHONY : all

# The main recursive preinstall target
preinstall:

.PHONY : preinstall

# The main recursive clean target
clean:

.PHONY : clean

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/DS_ACServer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/DS_ACServer/build

#=============================================================================
# Target rules for target CMakeFiles/ACServer.dir

# All Build rule for target.
CMakeFiles/ACServer.dir/all:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/depend
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/home/<USER>/code/DS_ACServer/build/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19 "Built target ACServer"
.PHONY : CMakeFiles/ACServer.dir/all

# Include target in all.
all: CMakeFiles/ACServer.dir/all

.PHONY : all

# Build rule for subdir invocation for target.
CMakeFiles/ACServer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/DS_ACServer/build/CMakeFiles 19
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/ACServer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/DS_ACServer/build/CMakeFiles 0
.PHONY : CMakeFiles/ACServer.dir/rule

# Convenience name for target.
ACServer: CMakeFiles/ACServer.dir/rule

.PHONY : ACServer

# clean rule for target.
CMakeFiles/ACServer.dir/clean:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/clean
.PHONY : CMakeFiles/ACServer.dir/clean

# clean rule for target.
clean: CMakeFiles/ACServer.dir/clean

.PHONY : clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

