# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.13

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/code/DS_ACServer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/code/DS_ACServer/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/DS_ACServer/build/CMakeFiles /home/<USER>/code/DS_ACServer/build/CMakeFiles/progress.marks
	$(MAKE) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/code/DS_ACServer/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named ACServer

# Build rule for target.
ACServer: cmake_check_build_system
	$(MAKE) -f CMakeFiles/Makefile2 ACServer
.PHONY : ACServer

# fast build rule for target.
ACServer/fast:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/build
.PHONY : ACServer/fast

checkip.o: checkip.cpp.o

.PHONY : checkip.o

# target to build an object file
checkip.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/checkip.cpp.o
.PHONY : checkip.cpp.o

checkip.i: checkip.cpp.i

.PHONY : checkip.i

# target to preprocess a source file
checkip.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/checkip.cpp.i
.PHONY : checkip.cpp.i

checkip.s: checkip.cpp.s

.PHONY : checkip.s

# target to generate assembly for a file
checkip.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/checkip.cpp.s
.PHONY : checkip.cpp.s

crc.o: crc.cpp.o

.PHONY : crc.o

# target to build an object file
crc.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/crc.cpp.o
.PHONY : crc.cpp.o

crc.i: crc.cpp.i

.PHONY : crc.i

# target to preprocess a source file
crc.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/crc.cpp.i
.PHONY : crc.cpp.i

crc.s: crc.cpp.s

.PHONY : crc.s

# target to generate assembly for a file
crc.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/crc.cpp.s
.PHONY : crc.cpp.s

ds_lsp.o: ds_lsp.cpp.o

.PHONY : ds_lsp.o

# target to build an object file
ds_lsp.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ds_lsp.cpp.o
.PHONY : ds_lsp.cpp.o

ds_lsp.i: ds_lsp.cpp.i

.PHONY : ds_lsp.i

# target to preprocess a source file
ds_lsp.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ds_lsp.cpp.i
.PHONY : ds_lsp.cpp.i

ds_lsp.s: ds_lsp.cpp.s

.PHONY : ds_lsp.s

# target to generate assembly for a file
ds_lsp.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ds_lsp.cpp.s
.PHONY : ds_lsp.cpp.s

gpio.o: gpio.cpp.o

.PHONY : gpio.o

# target to build an object file
gpio.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gpio.cpp.o
.PHONY : gpio.cpp.o

gpio.i: gpio.cpp.i

.PHONY : gpio.i

# target to preprocess a source file
gpio.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gpio.cpp.i
.PHONY : gpio.cpp.i

gpio.s: gpio.cpp.s

.PHONY : gpio.s

# target to generate assembly for a file
gpio.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gpio.cpp.s
.PHONY : gpio.cpp.s

gps.o: gps.cpp.o

.PHONY : gps.o

# target to build an object file
gps.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gps.cpp.o
.PHONY : gps.cpp.o

gps.i: gps.cpp.i

.PHONY : gps.i

# target to preprocess a source file
gps.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gps.cpp.i
.PHONY : gps.cpp.i

gps.s: gps.cpp.s

.PHONY : gps.s

# target to generate assembly for a file
gps.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/gps.cpp.s
.PHONY : gps.cpp.s

main.o: main.cpp.o

.PHONY : main.o

# target to build an object file
main.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/main.cpp.o
.PHONY : main.cpp.o

main.i: main.cpp.i

.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s

.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/main.cpp.s
.PHONY : main.cpp.s

map_update.o: map_update.cpp.o

.PHONY : map_update.o

# target to build an object file
map_update.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/map_update.cpp.o
.PHONY : map_update.cpp.o

map_update.i: map_update.cpp.i

.PHONY : map_update.i

# target to preprocess a source file
map_update.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/map_update.cpp.i
.PHONY : map_update.cpp.i

map_update.s: map_update.cpp.s

.PHONY : map_update.s

# target to generate assembly for a file
map_update.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/map_update.cpp.s
.PHONY : map_update.cpp.s

md5.o: md5.cpp.o

.PHONY : md5.o

# target to build an object file
md5.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/md5.cpp.o
.PHONY : md5.cpp.o

md5.i: md5.cpp.i

.PHONY : md5.i

# target to preprocess a source file
md5.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/md5.cpp.i
.PHONY : md5.cpp.i

md5.s: md5.cpp.s

.PHONY : md5.s

# target to generate assembly for a file
md5.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/md5.cpp.s
.PHONY : md5.cpp.s

n_tax.o: n_tax.cpp.o

.PHONY : n_tax.o

# target to build an object file
n_tax.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/n_tax.cpp.o
.PHONY : n_tax.cpp.o

n_tax.i: n_tax.cpp.i

.PHONY : n_tax.i

# target to preprocess a source file
n_tax.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/n_tax.cpp.i
.PHONY : n_tax.cpp.i

n_tax.s: n_tax.cpp.s

.PHONY : n_tax.s

# target to generate assembly for a file
n_tax.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/n_tax.cpp.s
.PHONY : n_tax.cpp.s

ntrip.o: ntrip.cpp.o

.PHONY : ntrip.o

# target to build an object file
ntrip.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ntrip.cpp.o
.PHONY : ntrip.cpp.o

ntrip.i: ntrip.cpp.i

.PHONY : ntrip.i

# target to preprocess a source file
ntrip.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ntrip.cpp.i
.PHONY : ntrip.cpp.i

ntrip.s: ntrip.cpp.s

.PHONY : ntrip.s

# target to generate assembly for a file
ntrip.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/ntrip.cpp.s
.PHONY : ntrip.cpp.s

readcfg.o: readcfg.cpp.o

.PHONY : readcfg.o

# target to build an object file
readcfg.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/readcfg.cpp.o
.PHONY : readcfg.cpp.o

readcfg.i: readcfg.cpp.i

.PHONY : readcfg.i

# target to preprocess a source file
readcfg.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/readcfg.cpp.i
.PHONY : readcfg.cpp.i

readcfg.s: readcfg.cpp.s

.PHONY : readcfg.s

# target to generate assembly for a file
readcfg.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/readcfg.cpp.s
.PHONY : readcfg.cpp.s

rk_filelog.o: rk_filelog.cpp.o

.PHONY : rk_filelog.o

# target to build an object file
rk_filelog.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/rk_filelog.cpp.o
.PHONY : rk_filelog.cpp.o

rk_filelog.i: rk_filelog.cpp.i

.PHONY : rk_filelog.i

# target to preprocess a source file
rk_filelog.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/rk_filelog.cpp.i
.PHONY : rk_filelog.cpp.i

rk_filelog.s: rk_filelog.cpp.s

.PHONY : rk_filelog.s

# target to generate assembly for a file
rk_filelog.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/rk_filelog.cpp.s
.PHONY : rk_filelog.cpp.s

save_data.o: save_data.cpp.o

.PHONY : save_data.o

# target to build an object file
save_data.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/save_data.cpp.o
.PHONY : save_data.cpp.o

save_data.i: save_data.cpp.i

.PHONY : save_data.i

# target to preprocess a source file
save_data.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/save_data.cpp.i
.PHONY : save_data.cpp.i

save_data.s: save_data.cpp.s

.PHONY : save_data.s

# target to generate assembly for a file
save_data.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/save_data.cpp.s
.PHONY : save_data.cpp.s

sm4.o: sm4.cpp.o

.PHONY : sm4.o

# target to build an object file
sm4.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/sm4.cpp.o
.PHONY : sm4.cpp.o

sm4.i: sm4.cpp.i

.PHONY : sm4.i

# target to preprocess a source file
sm4.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/sm4.cpp.i
.PHONY : sm4.cpp.i

sm4.s: sm4.cpp.s

.PHONY : sm4.s

# target to generate assembly for a file
sm4.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/sm4.cpp.s
.PHONY : sm4.cpp.s

test_dat.o: test_dat.cpp.o

.PHONY : test_dat.o

# target to build an object file
test_dat.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/test_dat.cpp.o
.PHONY : test_dat.cpp.o

test_dat.i: test_dat.cpp.i

.PHONY : test_dat.i

# target to preprocess a source file
test_dat.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/test_dat.cpp.i
.PHONY : test_dat.cpp.i

test_dat.s: test_dat.cpp.s

.PHONY : test_dat.s

# target to generate assembly for a file
test_dat.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/test_dat.cpp.s
.PHONY : test_dat.cpp.s

tinyxml2.o: tinyxml2.cpp.o

.PHONY : tinyxml2.o

# target to build an object file
tinyxml2.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/tinyxml2.cpp.o
.PHONY : tinyxml2.cpp.o

tinyxml2.i: tinyxml2.cpp.i

.PHONY : tinyxml2.i

# target to preprocess a source file
tinyxml2.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/tinyxml2.cpp.i
.PHONY : tinyxml2.cpp.i

tinyxml2.s: tinyxml2.cpp.s

.PHONY : tinyxml2.s

# target to generate assembly for a file
tinyxml2.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/tinyxml2.cpp.s
.PHONY : tinyxml2.cpp.s

udp_to_server.o: udp_to_server.cpp.o

.PHONY : udp_to_server.o

# target to build an object file
udp_to_server.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/udp_to_server.cpp.o
.PHONY : udp_to_server.cpp.o

udp_to_server.i: udp_to_server.cpp.i

.PHONY : udp_to_server.i

# target to preprocess a source file
udp_to_server.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/udp_to_server.cpp.i
.PHONY : udp_to_server.cpp.i

udp_to_server.s: udp_to_server.cpp.s

.PHONY : udp_to_server.s

# target to generate assembly for a file
udp_to_server.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/udp_to_server.cpp.s
.PHONY : udp_to_server.cpp.s

wdog.o: wdog.cpp.o

.PHONY : wdog.o

# target to build an object file
wdog.cpp.o:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/wdog.cpp.o
.PHONY : wdog.cpp.o

wdog.i: wdog.cpp.i

.PHONY : wdog.i

# target to preprocess a source file
wdog.cpp.i:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/wdog.cpp.i
.PHONY : wdog.cpp.i

wdog.s: wdog.cpp.s

.PHONY : wdog.s

# target to generate assembly for a file
wdog.cpp.s:
	$(MAKE) -f CMakeFiles/ACServer.dir/build.make CMakeFiles/ACServer.dir/wdog.cpp.s
.PHONY : wdog.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... rebuild_cache"
	@echo "... ACServer"
	@echo "... edit_cache"
	@echo "... checkip.o"
	@echo "... checkip.i"
	@echo "... checkip.s"
	@echo "... crc.o"
	@echo "... crc.i"
	@echo "... crc.s"
	@echo "... ds_lsp.o"
	@echo "... ds_lsp.i"
	@echo "... ds_lsp.s"
	@echo "... gpio.o"
	@echo "... gpio.i"
	@echo "... gpio.s"
	@echo "... gps.o"
	@echo "... gps.i"
	@echo "... gps.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... map_update.o"
	@echo "... map_update.i"
	@echo "... map_update.s"
	@echo "... md5.o"
	@echo "... md5.i"
	@echo "... md5.s"
	@echo "... n_tax.o"
	@echo "... n_tax.i"
	@echo "... n_tax.s"
	@echo "... ntrip.o"
	@echo "... ntrip.i"
	@echo "... ntrip.s"
	@echo "... readcfg.o"
	@echo "... readcfg.i"
	@echo "... readcfg.s"
	@echo "... rk_filelog.o"
	@echo "... rk_filelog.i"
	@echo "... rk_filelog.s"
	@echo "... save_data.o"
	@echo "... save_data.i"
	@echo "... save_data.s"
	@echo "... sm4.o"
	@echo "... sm4.i"
	@echo "... sm4.s"
	@echo "... test_dat.o"
	@echo "... test_dat.i"
	@echo "... test_dat.s"
	@echo "... tinyxml2.o"
	@echo "... tinyxml2.i"
	@echo "... tinyxml2.s"
	@echo "... udp_to_server.o"
	@echo "... udp_to_server.i"
	@echo "... udp_to_server.s"
	@echo "... wdog.o"
	@echo "... wdog.i"
	@echo "... wdog.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

