#ifndef _GPS_H
#define _GPS_H

typedef struct{
	int year; 
	int month; 
	int day;
	int hour;
	int minute;
	int second;
}GPS_TIME;

typedef struct
{
    double longitude; //经度
    double latitude; //纬度
	unsigned short speed;//速度
	unsigned short height;//海拔
	GPS_TIME GTime;//时间
	char State;//定位状态 0=未定位，1=非差分定位，2=差分定位，3=无效PPS，4=固定 5=浮动 6=正在估算
    char StarNum;//卫星数
    unsigned short BaseStationID;//差分基站ID
    unsigned short BaseStationTime;//差分基站时效
}GPS_INFO;

int SerialInit(char* com,int baud);
int SerialSend(int comfd,char* buf,int len);
void * Thread_GPS(void *lpPara);


#endif
